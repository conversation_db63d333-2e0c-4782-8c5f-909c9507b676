import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Instagram credentials
    INSTA_USERNAME = os.getenv("INSTA_USERNAME")
    INSTA_PASSWORD = os.getenv("INSTA_PASSWORD")

    # API Keys
    ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY")
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

    # New API Keys for Enhanced Agents
    IMAGINE_ART_API_KEY = os.getenv("IMAGINE_ART_API_KEY")
    PIXABAY_API_KEY = os.getenv("PIXABAY_API_KEY")
    UNSPLASH_ACCESS_KEY = os.getenv("UNSPLASH_ACCESS_KEY")
    FREESOUND_API_KEY = os.getenv("FREESOUND_API_KEY")
    GOOGLE_CLOUD_TTS_KEY = os.getenv("GOOGLE_CLOUD_TTS_KEY")
    AZURE_SPEECH_KEY = os.getenv("AZURE_SPEECH_KEY")
    AZURE_SPEECH_REGION = os.getenv("AZURE_SPEECH_REGION", "eastus")

    # Existing API Keys
    NEWSAPI_KEY = os.getenv("NEWSAPI_KEY")
    RAPIDAPI_KEY = os.getenv("RAPIDAPI_KEY")

    # Paths
    ASSETS_PATH = "assets/"
    TEMP_PATH = "temp/"
    LOGS_PATH = "logs/"

    # Posting schedule (IST)
    POSTING_TIMES = ["08:00", "14:00", "20:00"]

    # Voice Settings for ElevenLabs
    ELEVENLABS_VOICE_ID = "grandpa_spuds_oxley"  # This would be the actual voice ID

    # Video Production Settings
    VIDEO_RESOLUTION = (1080, 1920)  # Instagram Reels format
    VIDEO_FPS = 30
    DEFAULT_VIDEO_DURATION = 20