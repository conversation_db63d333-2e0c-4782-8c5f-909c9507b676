from crewai.tools import BaseTool
from typing import Type
from pydantic import BaseModel, Field
import os
import time
import json
import numpy as np
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance

# Import MoviePy components
try:
    from moviepy.editor import (VideoClip, ImageClip, AudioFileClip, CompositeVideoClip, 
                               CompositeAudioClip, ColorClip, TextClip, concatenate_videoclips)
    from moviepy.video.fx import resize, fadein, fadeout
    from moviepy.audio.fx import volumex
    MOVIEPY_AVAILABLE = True
except ImportError as e:
    print(f"MoviePy import error: {e}")
    MOVIEPY_AVAILABLE = False

class VideoProductionInput(BaseModel):
    quote: str = Field(description="Quote text for the video")
    image_path: str = Field(description="Path to the background image")
    voice_path: str = Field(description="Path to the voiceover audio file")
    music_path: str = Field(description="Path to the background music file")
    video_length: int = Field(default=20, description="Video length in seconds")
    transition_effects: str = Field(default="fade", description="Transition effects to use")
    text_overlay_timing: str = Field(default="sync_with_voice", description="Text overlay timing")
    audio_sync: str = Field(default="balanced", description="Audio synchronization settings")

class VideoProductionTool(BaseTool):
    name: str = "Video Production Tool"
    description: str = "Creates professional cinematic videos combining images, voiceovers, and music"
    args_schema: Type[BaseModel] = VideoProductionInput

    def _run(self, quote: str, image_path: str, voice_path: str, music_path: str,
             video_length: int = 20, transition_effects: str = "fade",
             text_overlay_timing: str = "sync_with_voice", audio_sync: str = "balanced") -> str:
        try:
            # Ensure temp directory exists
            os.makedirs("temp", exist_ok=True)
            
            if not MOVIEPY_AVAILABLE:
                return self._create_video_specifications(quote, image_path, voice_path, music_path, 
                                                       video_length, transition_effects, 
                                                       text_overlay_timing, audio_sync)
            
            # Extract production parameters
            production_params = self._extract_production_parameters(
                quote, video_length, transition_effects, text_overlay_timing, audio_sync
            )
            
            # Create the video
            video_result = self._create_professional_video(
                quote, image_path, voice_path, music_path, production_params
            )
            
            return video_result
            
        except Exception as e:
            return f"Error in video production: {str(e)}"

    def _extract_production_parameters(self, quote: str, video_length: int, 
                                     transition_effects: str, text_overlay_timing: str, 
                                     audio_sync: str) -> dict:
        """Extract and optimize production parameters"""
        
        quote_words = quote.split()
        word_count = len(quote_words)
        
        # Calculate optimal video length based on content
        estimated_speech_time = word_count * 0.6  # 0.6 seconds per word for contemplative pace
        optimal_length = max(video_length, int(estimated_speech_time + 3))  # +3 for intro/outro
        
        # Determine text animation style based on quote content
        quote_lower = quote.lower()
        if any(word in quote_lower for word in ['peace', 'calm', 'tranquil']):
            text_animation = "gentle_fade"
        elif any(word in quote_lower for word in ['wisdom', 'truth', 'enlightenment']):
            text_animation = "word_by_word_reveal"
        elif any(word in quote_lower for word in ['action', 'now', 'moment']):
            text_animation = "dynamic_entrance"
        else:
            text_animation = "smooth_fade"
        
        # Determine visual effects based on content
        if any(word in quote_lower for word in ['breath', 'breathing', 'life']):
            visual_effects = "breathing_zoom"
        elif any(word in quote_lower for word in ['flow', 'water', 'river']):
            visual_effects = "gentle_pan"
        elif any(word in quote_lower for word in ['light', 'bright', 'illuminate']):
            visual_effects = "brightness_pulse"
        else:
            visual_effects = "subtle_zoom"
        
        return {
            'optimal_length': optimal_length,
            'text_animation': text_animation,
            'visual_effects': visual_effects,
            'fade_in_duration': 1.0,
            'fade_out_duration': 1.5,
            'text_display_timing': self._calculate_text_timing(quote_words, estimated_speech_time),
            'audio_balance': self._get_audio_balance(audio_sync),
            'aspect_ratio': (9, 16),  # Instagram Reels format
            'resolution': (1080, 1920),
            'fps': 30
        }

    def _calculate_text_timing(self, words: list, speech_duration: float) -> list:
        """Calculate timing for text overlay based on speech"""
        
        timings = []
        words_per_segment = max(1, len(words) // 3)  # Divide into 3 segments
        time_per_segment = speech_duration / 3
        
        for i in range(0, len(words), words_per_segment):
            segment_words = words[i:i + words_per_segment]
            start_time = (i // words_per_segment) * time_per_segment + 0.5  # +0.5 for intro
            end_time = start_time + time_per_segment
            
            timings.append({
                'text': ' '.join(segment_words),
                'start': start_time,
                'end': end_time
            })
        
        return timings

    def _get_audio_balance(self, audio_sync: str) -> dict:
        """Get audio balance settings"""
        
        balance_settings = {
            'voice_prominent': {'voice_volume': 1.0, 'music_volume': 0.2},
            'balanced': {'voice_volume': 1.0, 'music_volume': 0.3},
            'music_prominent': {'voice_volume': 1.0, 'music_volume': 0.5},
            'voice_only': {'voice_volume': 1.0, 'music_volume': 0.0}
        }
        
        return balance_settings.get(audio_sync, balance_settings['balanced'])

    def _create_professional_video(self, quote: str, image_path: str, voice_path: str, 
                                 music_path: str, params: dict) -> str:
        """Create the professional video with all elements"""
        try:
            # Load and prepare background image
            background_clip = self._prepare_background_image(image_path, params)
            
            # Load and prepare audio
            voice_clip, music_clip = self._prepare_audio(voice_path, music_path, params)
            
            # Create text overlays
            text_clips = self._create_text_overlays(quote, params)
            
            # Combine all elements
            final_video = self._combine_video_elements(background_clip, text_clips, voice_clip, music_clip, params)
            
            # Export the final video
            output_path = f"temp/professional_video_{int(time.time())}.mp4"
            final_video.write_videofile(
                output_path,
                fps=params['fps'],
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp/temp_audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )
            
            # Create production report
            report_path = self._create_production_report(output_path, params, quote)
            
            return f"Created professional video: {output_path} with production report: {report_path}"
            
        except Exception as e:
            return f"Error creating professional video: {str(e)}"

    def _prepare_background_image(self, image_path: str, params: dict) -> ImageClip:
        """Prepare and enhance the background image"""
        try:
            if not os.path.exists(image_path):
                # Create a fallback gradient background
                return self._create_fallback_background(params)
            
            # Load and enhance the image
            enhanced_image_path = self._enhance_image(image_path, params)
            
            # Create video clip from enhanced image
            img_clip = ImageClip(enhanced_image_path, duration=params['optimal_length'])
            
            # Apply visual effects
            if params['visual_effects'] == 'breathing_zoom':
                img_clip = self._apply_breathing_effect(img_clip)
            elif params['visual_effects'] == 'gentle_pan':
                img_clip = self._apply_pan_effect(img_clip)
            elif params['visual_effects'] == 'brightness_pulse':
                img_clip = self._apply_brightness_pulse(img_clip)
            else:
                img_clip = self._apply_subtle_zoom(img_clip)
            
            # Apply fade transitions
            img_clip = img_clip.fadein(params['fade_in_duration']).fadeout(params['fade_out_duration'])
            
            return img_clip
            
        except Exception as e:
            print(f"Error preparing background image: {e}")
            return self._create_fallback_background(params)

    def _enhance_image(self, image_path: str, params: dict) -> str:
        """Enhance the image for video production"""
        try:
            # Open and process the image
            img = Image.open(image_path)
            
            # Resize to video resolution
            target_size = params['resolution']
            img = img.resize(target_size, Image.Resampling.LANCZOS)
            
            # Enhance the image
            enhancer = ImageEnhance.Color(img)
            img = enhancer.enhance(1.1)  # Slight color boost
            
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(1.05)  # Slight contrast boost
            
            enhancer = ImageEnhance.Brightness(img)
            img = enhancer.enhance(1.02)  # Slight brightness boost
            
            # Apply subtle blur for cinematic effect
            img = img.filter(ImageFilter.GaussianBlur(radius=0.5))
            
            # Save enhanced image
            enhanced_path = f"temp/enhanced_bg_{int(time.time())}.png"
            img.save(enhanced_path, quality=95)
            
            return enhanced_path
            
        except Exception as e:
            print(f"Error enhancing image: {e}")
            return image_path

    def _create_fallback_background(self, params: dict) -> ColorClip:
        """Create a fallback gradient background"""
        # Create a simple color background
        return ColorClip(size=params['resolution'], color=(139, 69, 19), duration=params['optimal_length'])

    def _apply_breathing_effect(self, clip: ImageClip) -> ImageClip:
        """Apply breathing zoom effect"""
        def breathing_effect(get_frame, t):
            # Create breathing effect with sine wave
            scale_factor = 1.0 + 0.02 * np.sin(2 * np.pi * t / 4)  # 4-second breathing cycle
            frame = get_frame(t)
            # This would need proper implementation with frame scaling
            return frame
        
        return clip.fl(breathing_effect)

    def _apply_pan_effect(self, clip: ImageClip) -> ImageClip:
        """Apply gentle pan effect"""
        # Simple implementation - would need more sophisticated panning
        return clip

    def _apply_brightness_pulse(self, clip: ImageClip) -> ImageClip:
        """Apply brightness pulse effect"""
        # Simple implementation - would need brightness modulation
        return clip

    def _apply_subtle_zoom(self, clip: ImageClip) -> ImageClip:
        """Apply subtle zoom effect"""
        def zoom_effect(t):
            return 1 + 0.05 * (t / clip.duration)  # Gradual 5% zoom over duration
        
        return clip.resize(zoom_effect)

    def _prepare_audio(self, voice_path: str, music_path: str, params: dict):
        """Prepare and balance audio tracks"""
        try:
            voice_clip = None
            music_clip = None
            
            # Load voice audio if available
            if voice_path and os.path.exists(voice_path):
                voice_clip = AudioFileClip(voice_path)
                voice_clip = voice_clip.volumex(params['audio_balance']['voice_volume'])
            
            # Load music audio if available
            if music_path and os.path.exists(music_path):
                music_clip = AudioFileClip(music_path)
                music_clip = music_clip.volumex(params['audio_balance']['music_volume'])
                
                # Loop music if it's shorter than video
                if music_clip.duration < params['optimal_length']:
                    music_clip = music_clip.loop(duration=params['optimal_length'])
                else:
                    music_clip = music_clip.subclip(0, params['optimal_length'])
                
                # Apply fade in/out to music
                music_clip = music_clip.fadein(2.0).fadeout(2.0)
            
            return voice_clip, music_clip
            
        except Exception as e:
            print(f"Error preparing audio: {e}")
            return None, None

    def _create_text_overlays(self, quote: str, params: dict) -> list:
        """Create text overlay clips"""
        try:
            text_clips = []
            
            # Create text clips based on timing
            for timing in params['text_display_timing']:
                try:
                    text_clip = TextClip(
                        timing['text'],
                        fontsize=60,
                        color='white',
                        font='Arial-Bold',
                        stroke_color='black',
                        stroke_width=2
                    ).set_position('center').set_start(timing['start']).set_end(timing['end'])
                    
                    # Apply text animation
                    if params['text_animation'] == 'gentle_fade':
                        text_clip = text_clip.fadein(0.5).fadeout(0.5)
                    elif params['text_animation'] == 'dynamic_entrance':
                        text_clip = text_clip.fadein(0.3)
                    
                    text_clips.append(text_clip)
                    
                except Exception as text_error:
                    print(f"Error creating text clip: {text_error}")
                    # Create simple text overlay as fallback
                    continue
            
            # If no text clips were created, create a simple full quote overlay
            if not text_clips:
                try:
                    full_text_clip = TextClip(
                        quote,
                        fontsize=50,
                        color='white',
                        font='Arial',
                        stroke_color='black',
                        stroke_width=1
                    ).set_position('center').set_duration(params['optimal_length'] - 2).set_start(1)
                    
                    text_clips.append(full_text_clip)
                except:
                    pass  # If text creation fails completely, continue without text
            
            return text_clips
            
        except Exception as e:
            print(f"Error creating text overlays: {e}")
            return []

    def _combine_video_elements(self, background_clip, text_clips: list, voice_clip, music_clip, params: dict):
        """Combine all video elements into final composition"""
        try:
            # Start with background
            video_clips = [background_clip]
            
            # Add text overlays
            video_clips.extend(text_clips)
            
            # Create composite video
            final_video = CompositeVideoClip(video_clips, size=params['resolution'])
            
            # Add audio
            audio_clips = []
            if voice_clip:
                audio_clips.append(voice_clip)
            if music_clip:
                audio_clips.append(music_clip)
            
            if audio_clips:
                final_audio = CompositeAudioClip(audio_clips)
                final_video = final_video.set_audio(final_audio)
            
            # Set final duration
            final_video = final_video.set_duration(params['optimal_length'])
            
            return final_video
            
        except Exception as e:
            print(f"Error combining video elements: {e}")
            # Return just the background as fallback
            return background_clip

    def _create_production_report(self, video_path: str, params: dict, quote: str) -> str:
        """Create a detailed production report"""
        try:
            report_path = f"temp/production_report_{int(time.time())}.json"
            
            report = {
                "video_file": video_path,
                "production_timestamp": time.time(),
                "quote": quote,
                "technical_specifications": {
                    "resolution": f"{params['resolution'][0]}x{params['resolution'][1]}",
                    "aspect_ratio": f"{params['aspect_ratio'][0]}:{params['aspect_ratio'][1]}",
                    "fps": params['fps'],
                    "duration": params['optimal_length'],
                    "codec": "H.264",
                    "audio_codec": "AAC"
                },
                "creative_elements": {
                    "text_animation": params['text_animation'],
                    "visual_effects": params['visual_effects'],
                    "fade_in_duration": params['fade_in_duration'],
                    "fade_out_duration": params['fade_out_duration']
                },
                "audio_settings": params['audio_balance'],
                "text_timing": params['text_display_timing']
            }
            
            with open(report_path, 'w') as f:
                json.dump(report, f, indent=2)
            
            return report_path
            
        except Exception as e:
            return f"Error creating production report: {str(e)}"

    def _create_video_specifications(self, quote: str, image_path: str, voice_path: str, 
                                   music_path: str, video_length: int, transition_effects: str,
                                   text_overlay_timing: str, audio_sync: str) -> str:
        """Create video specifications when MoviePy is not available"""
        try:
            specs_path = f"temp/video_production_specs_{int(time.time())}.json"
            
            specifications = {
                "video_production_requirements": {
                    "quote": quote,
                    "assets": {
                        "background_image": image_path,
                        "voiceover_audio": voice_path,
                        "background_music": music_path
                    },
                    "video_settings": {
                        "length": video_length,
                        "resolution": "1080x1920",
                        "aspect_ratio": "9:16",
                        "fps": 30,
                        "format": "MP4"
                    },
                    "effects": {
                        "transitions": transition_effects,
                        "text_timing": text_overlay_timing,
                        "audio_sync": audio_sync
                    }
                },
                "production_notes": [
                    "MoviePy library not available for automatic video creation",
                    "Use these specifications with video editing software",
                    "Recommended software: DaVinci Resolve, Adobe Premiere, or OpenShot",
                    "Apply fade-in/fade-out transitions for professional look",
                    "Balance audio levels: voice at 100%, music at 25-30%"
                ]
            }
            
            with open(specs_path, 'w') as f:
                json.dump(specifications, f, indent=2)
            
            return f"Created video production specifications: {specs_path} (MoviePy not available for automatic creation)"
            
        except Exception as e:
            return f"Error creating video specifications: {str(e)}"
