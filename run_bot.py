#!/usr/bin/env python3
"""
<PERSON> Quotes Instagram Bot - Enhanced Edition
Startup script with easy mode selection
"""

import os
import sys
import subprocess
from datetime import datetime

def show_banner():
    """Show startup banner"""
    print("🌟" * 30)
    print("🌟 BUDDHA QUOTES INSTAGRAM BOT 🌟")
    print("🌟    ENHANCED EDITION v2.0     🌟")
    print("🌟" * 30)
    print()

def show_menu():
    """Show interactive menu"""
    print("📋 SELECT OPERATION MODE:")
    print("=" * 40)
    print("1. 🧪 Test Mode - Single test run")
    print("2. 🔍 Verify Only - Check components")
    print("3. 🚀 Development - Test + Schedule")
    print("4. 🏭 Production - Direct to schedule")
    print("5. 📊 Full Operation - Complete workflow")
    print("6. 🛠️  Custom - Enter custom arguments")
    print("7. ❌ Exit")
    print("=" * 40)

def get_user_choice():
    """Get user menu choice"""
    while True:
        try:
            choice = input("Enter your choice (1-7): ").strip()
            if choice in ['1', '2', '3', '4', '5', '6', '7']:
                return int(choice)
            else:
                print("❌ Invalid choice. Please enter 1-7.")
        except KeyboardInterrupt:
            print("\n🛑 Cancelled by user")
            sys.exit(0)
        except Exception:
            print("❌ Invalid input. Please enter a number 1-7.")

def check_environment():
    """Check if environment is properly configured"""
    print("🔧 ENVIRONMENT CHECK:")
    print("-" * 30)
    
    # Check for .env file
    if os.path.exists('.env'):
        print("✅ .env file found")
    else:
        print("⚠️  .env file not found - using system environment variables")
    
    # Check critical environment variables
    critical_vars = ['OPENAI_API_KEY', 'INSTA_USERNAME', 'INSTA_PASSWORD']
    missing_vars = []
    
    for var in critical_vars:
        if os.getenv(var):
            print(f"✅ {var} configured")
        else:
            print(f"❌ {var} missing")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n⚠️  WARNING: Missing critical variables: {', '.join(missing_vars)}")
        print("   The bot may not function properly without these.")
        
        response = input("\nContinue anyway? (y/N): ").strip().lower()
        if response != 'y':
            print("🛑 Cancelled by user")
            sys.exit(0)
    
    print("✅ Environment check completed\n")

def run_command(args):
    """Run the main bot with specified arguments"""
    cmd = [sys.executable, 'main.py'] + args
    
    print(f"🚀 EXECUTING: {' '.join(cmd)}")
    print("=" * 50)
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    try:
        # Run the command
        result = subprocess.run(cmd, check=False)
        return result.returncode == 0
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        return False
    except Exception as e:
        print(f"💥 Error running command: {e}")
        return False

def main():
    """Main interactive launcher"""
    show_banner()
    
    # Check environment first
    check_environment()
    
    while True:
        show_menu()
        choice = get_user_choice()
        
        if choice == 1:  # Test Mode
            print("🧪 SINGLE TEST MODE")
            print("Running one test execution and exiting...")
            success = run_command(['--test', '--log-level', 'INFO'])
            if success:
                print("🎉 Test completed successfully!")
            else:
                print("💥 Test failed!")
            break
            
        elif choice == 2:  # Verify Only
            print("🔍 VERIFICATION MODE")
            print("Checking all components and dependencies...")
            success = run_command(['--verify-only', '--log-level', 'INFO'])
            if success:
                print("✅ All components verified!")
            else:
                print("❌ Some components failed verification!")
            break
            
        elif choice == 3:  # Development
            print("🚀 DEVELOPMENT MODE")
            print("Running test first, then starting scheduler...")
            success = run_command(['--development', '--log-level', 'INFO'])
            break
            
        elif choice == 4:  # Production
            print("🏭 PRODUCTION MODE")
            print("Starting scheduler directly (no test run)...")
            success = run_command(['--production', '--schedule-only', '--log-level', 'INFO'])
            break
            
        elif choice == 5:  # Full Operation
            print("📊 FULL OPERATION MODE")
            print("Complete workflow with all features...")
            success = run_command(['--log-level', 'INFO'])
            break
            
        elif choice == 6:  # Custom
            print("🛠️  CUSTOM MODE")
            print("Available arguments:")
            print("  --test              # Single test run")
            print("  --verify-only       # Component verification only")
            print("  --schedule-only     # Skip test, go to scheduling")
            print("  --production        # Force production mode")
            print("  --development       # Force development mode")
            print("  --log-level DEBUG   # Set log level (DEBUG/INFO/WARNING/ERROR)")
            print()
            
            custom_args = input("Enter custom arguments: ").strip()
            if custom_args:
                args = custom_args.split()
                print(f"🛠️  Running with custom arguments: {args}")
                success = run_command(args)
            else:
                print("❌ No arguments provided")
                continue
            break
            
        elif choice == 7:  # Exit
            print("👋 Goodbye!")
            sys.exit(0)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"💥 Unexpected error: {e}")
        sys.exit(1)
