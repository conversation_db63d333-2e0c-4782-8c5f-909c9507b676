#!/usr/bin/env python3
"""
Test script for Enhanced Buddha Quotes Instagram Bot
Tests all new specialized agents and their tools
"""

import os
import sys
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_image_generation_tool():
    """Test the Image Generation Tool"""
    print("\n🎨 Testing Image Generation Tool...")
    
    try:
        from tools.image_tools import ImageGenerationTool
        
        tool = ImageGenerationTool()
        
        # Test with a sample quote
        test_quote = "Peace comes from within. Do not seek it without."
        
        result = tool._run(
            quote=test_quote,
            style="serene",
            color_scheme="warm earth tones",
            mood="peaceful",
            subject_matter="Buddha silhouette",
            composition="centered"
        )
        
        print(f"✅ Image Generation Tool Result: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Image Generation Tool Error: {e}")
        return False

def test_voice_synthesis_tool():
    """Test the Voice Synthesis Tool"""
    print("\n🎙️ Testing Voice Synthesis Tool...")
    
    try:
        from tools.voice_tools import VoiceSynthesisTool
        
        tool = VoiceSynthesisTool()
        
        # Test with a sample quote
        test_quote = "The mind is everything. What you think you become."
        
        result = tool._run(
            quote=test_quote,
            speech_pace="moderate",
            emotional_tone="wise and warm",
            emphasis_points=["mind", "think", "become"],
            pause_locations=[2, 6]
        )
        
        print(f"✅ Voice Synthesis Tool Result: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Voice Synthesis Tool Error: {e}")
        return False

def test_music_selection_tool():
    """Test the Music Selection Tool"""
    print("\n🎵 Testing Music Selection Tool...")
    
    try:
        from tools.music_tools import MusicSelectionTool
        
        tool = MusicSelectionTool()
        
        # Test with a sample quote
        test_quote = "In the end, only three things matter: how much you loved, how gently you lived, and how gracefully you let go."
        
        result = tool._run(
            quote=test_quote,
            genre="ambient",
            tempo="slow",
            mood="contemplative",
            duration=25,
            volume_level="background"
        )
        
        print(f"✅ Music Selection Tool Result: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Music Selection Tool Error: {e}")
        return False

def test_video_production_tool():
    """Test the Video Production Tool"""
    print("\n🎬 Testing Video Production Tool...")
    
    try:
        from tools.video_production_tools import VideoProductionTool
        
        tool = VideoProductionTool()
        
        # Test with sample paths (these may not exist, but tool should handle gracefully)
        test_quote = "Happiness does not depend on what you have or who you are."
        
        result = tool._run(
            quote=test_quote,
            image_path="temp/test_image.png",
            voice_path="temp/test_voice.mp3",
            music_path="temp/test_music.mp3",
            video_length=20,
            transition_effects="fade",
            text_overlay_timing="sync_with_voice",
            audio_sync="balanced"
        )
        
        print(f"✅ Video Production Tool Result: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Video Production Tool Error: {e}")
        return False

def test_enhanced_crew():
    """Test the Enhanced Buddha Quote Crew"""
    print("\n🤖 Testing Enhanced Buddha Quote Crew...")
    
    try:
        from agents.crew_agents import BuddhaQuoteCrew
        
        # Create crew instance
        crew_instance = BuddhaQuoteCrew()
        
        # Create the crew (this tests agent and task creation)
        crew = crew_instance.create_crew()
        
        print(f"✅ Enhanced Crew Created Successfully")
        print(f"   - Agents: {len(crew.agents)}")
        print(f"   - Tasks: {len(crew.tasks)}")
        
        # List all agents
        print("\n📋 Agent List:")
        for i, agent in enumerate(crew.agents, 1):
            print(f"   {i}. {agent.role}")
        
        # List all tasks
        print("\n📋 Task List:")
        for i, task in enumerate(crew.tasks, 1):
            print(f"   {i}. {task.description[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced Crew Error: {e}")
        return False

def test_api_configurations():
    """Test API configurations and environment setup"""
    print("\n🔧 Testing API Configurations...")
    
    # Check required environment variables
    required_vars = ['OPENAI_API_KEY', 'INSTA_USERNAME', 'INSTA_PASSWORD']
    optional_vars = [
        'ELEVENLABS_API_KEY', 'IMAGINE_ART_API_KEY', 'PIXABAY_API_KEY',
        'UNSPLASH_ACCESS_KEY', 'GOOGLE_CLOUD_TTS_KEY', 'AZURE_SPEECH_KEY',
        'FREESOUND_API_KEY', 'NEWSAPI_KEY', 'RAPIDAPI_KEY'
    ]
    
    print("\n📋 Required Environment Variables:")
    all_required_present = True
    for var in required_vars:
        value = os.getenv(var)
        if value and value != f'your_{var.lower()}_here':
            print(f"   ✅ {var}: Configured")
        else:
            print(f"   ❌ {var}: Not configured")
            all_required_present = False
    
    print("\n📋 Optional Environment Variables:")
    optional_count = 0
    for var in optional_vars:
        value = os.getenv(var)
        if value and value != f'your_{var.lower()}_here':
            print(f"   ✅ {var}: Configured")
            optional_count += 1
        else:
            print(f"   ⚠️  {var}: Not configured (fallback will be used)")
    
    print(f"\n📊 Configuration Summary:")
    print(f"   - Required APIs: {'All configured' if all_required_present else 'Missing some'}")
    print(f"   - Optional APIs: {optional_count}/{len(optional_vars)} configured")
    print(f"   - Fallback Coverage: {'Excellent' if optional_count >= 3 else 'Good' if optional_count >= 1 else 'Basic'}")
    
    return all_required_present

def test_dependencies():
    """Test if all required dependencies are installed"""
    print("\n📦 Testing Dependencies...")
    
    dependencies = [
        ('crewai', 'CrewAI framework'),
        ('PIL', 'Pillow for image processing'),
        ('requests', 'HTTP requests'),
        ('numpy', 'Numerical computing'),
        ('moviepy', 'Video processing (optional)'),
        ('elevenlabs', 'ElevenLabs API client (optional)'),
        ('opencv-python', 'OpenCV for video processing (optional)')
    ]
    
    missing_deps = []
    optional_missing = []
    
    for dep, description in dependencies:
        try:
            __import__(dep)
            print(f"   ✅ {dep}: {description}")
        except ImportError:
            if 'optional' in description.lower():
                print(f"   ⚠️  {dep}: {description} - Missing (fallback available)")
                optional_missing.append(dep)
            else:
                print(f"   ❌ {dep}: {description} - Missing (required)")
                missing_deps.append(dep)
    
    if missing_deps:
        print(f"\n❌ Missing required dependencies: {', '.join(missing_deps)}")
        print("   Run: pip install -r requirements.txt")
        return False
    elif optional_missing:
        print(f"\n⚠️  Missing optional dependencies: {', '.join(optional_missing)}")
        print("   Enhanced features may use fallbacks")
        return True
    else:
        print("\n✅ All dependencies installed")
        return True

def main():
    """Main test function"""
    print("🚀 Buddha Quotes Instagram Bot - Enhanced Edition Test Suite")
    print("=" * 70)
    
    # Track test results
    test_results = []
    
    # Test dependencies first
    test_results.append(("Dependencies", test_dependencies()))
    
    # Test API configurations
    test_results.append(("API Configuration", test_api_configurations()))
    
    # Test individual tools
    test_results.append(("Image Generation Tool", test_image_generation_tool()))
    test_results.append(("Voice Synthesis Tool", test_voice_synthesis_tool()))
    test_results.append(("Music Selection Tool", test_music_selection_tool()))
    test_results.append(("Video Production Tool", test_video_production_tool()))
    
    # Test enhanced crew
    test_results.append(("Enhanced Crew", test_enhanced_crew()))
    
    # Print summary
    print("\n" + "=" * 70)
    print("📊 TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The enhanced bot is ready to use.")
    elif passed >= total * 0.7:
        print("⚠️  Most tests passed. Some features may use fallbacks.")
    else:
        print("❌ Several tests failed. Please check configuration and dependencies.")
    
    print("\n💡 Next Steps:")
    if passed < total:
        print("   1. Install missing dependencies: pip install -r requirements.txt")
        print("   2. Configure missing API keys in .env file")
        print("   3. Re-run this test script")
    else:
        print("   1. Run the enhanced bot: python main.py")
        print("   2. Check logs for detailed operation info")
        print("   3. Monitor Instagram for posted content")

if __name__ == "__main__":
    main()
