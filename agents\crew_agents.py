from crewai import Agent, Task, Crew, Process
from tools.content_tools import TrendResearchTool, QuoteGeneratorTool, SocialMediaTrendsTool
from tools.video_tools import VideoCreatorTool
from tools.instagram_tools import InstagramPostTool
from tools.image_tools import ImageGenerationTool
from tools.voice_tools import VoiceSynthesisTool
from tools.music_tools import MusicSelectionTool
from tools.video_production_tools import VideoProductionTool
import os

# Initialize tools
trend_tool = TrendResearchTool()
quote_tool = QuoteGeneratorTool()
social_trends_tool = SocialMediaTrendsTool()
video_tool = VideoCreatorTool()
instagram_tool = InstagramPostTool()
image_tool = ImageGenerationTool()
voice_tool = VoiceSynthesisTool()
music_tool = MusicSelectionTool()
video_production_tool = VideoProductionTool()

# Define Agents
content_researcher = Agent(
    role='Content Researcher',
    goal='Research trending topics and hashtags related to Buddha quotes and mindfulness',
    backstory="""You are an expert content researcher specializing in spiritual and
    motivational content. You analyze trends from Google Trends and social media platforms
    to find the most engaging topics and hashtags for spiritual content.""",
    tools=[trend_tool, social_trends_tool],
    verbose=True
)

quote_creator = Agent(
    role='Quote Creator',
    goal='Generate authentic Buddha-style motivational quotes',
    backstory="""You are a spiritual content creator with deep knowledge of Buddhist
    philosophy. You create meaningful quotes that inspire peace and mindfulness.""",
    tools=[quote_tool],
    verbose=True
)

video_producer = Agent(
    role='Video Producer',
    goal='Create engaging video content from quotes',
    backstory="""You are a skilled video producer specializing in spiritual content.
    You create visually appealing videos that complement motivational quotes.""",
    tools=[video_tool],
    verbose=True
)

social_media_manager = Agent(
    role='Social Media Manager',
    goal='Post content to Instagram with optimal engagement',
    backstory="""You are a social media expert who knows how to maximize engagement
    on Instagram through strategic posting and hashtag usage.""",
    tools=[instagram_tool],
    verbose=True
)

# New Specialized Agents
image_generation_agent = Agent(
    role='Image Generation Specialist',
    goal='Create visually stunning images for social media posts using AI image generation',
    backstory="""You are a creative visual artist specializing in AI-generated imagery for social media.
    You have expertise in visual composition, color theory, and creating images that perfectly complement
    inspirational quotes. You understand how to extract specific visual parameters like style, mood,
    color schemes, and composition details to create compelling images that resonate with spiritual
    and motivational content audiences.""",
    tools=[image_tool],
    verbose=True
)

voice_synthesis_agent = Agent(
    role='Voice Synthesis Specialist',
    goal='Generate high-quality voiceovers for quote-based content using ElevenLabs AI',
    backstory="""You are an expert in voice synthesis and audio production, specializing in creating
    compelling voiceovers for inspirational content. You understand how to analyze text to determine
    optimal speech pace, emotional tone, emphasis points, and natural pause locations. You work with
    the "Grandpa Spuds Oxley" voice model to create warm, wise, and engaging narrations that bring
    Buddha quotes and spiritual wisdom to life.""",
    tools=[voice_tool],
    verbose=True
)

music_selection_agent = Agent(
    role='Music Curation Specialist',
    goal='Select perfect background music for inspirational video content',
    backstory="""You are a music curator with expertise in selecting royalty-free background music
    that enhances spiritual and motivational content. You understand how music genre, tempo, mood,
    and volume levels affect viewer engagement and emotional response. You specialize in finding
    soft, ambient, and cinematic music from Pixabay that perfectly complements inspirational quotes
    and creates an immersive viewing experience.""",
    tools=[music_tool],
    verbose=True
)

video_production_agent = Agent(
    role='Video Production Director',
    goal='Create professional cinematic videos combining images, voiceovers, and music',
    backstory="""You are a professional video producer specializing in creating high-quality social media
    content. You have expertise in video editing, timing, transitions, and audio-visual synchronization.
    You understand how to combine AI-generated images, voiceovers, and background music into cohesive,
    engaging videos with professional effects like fade transitions, text overlays, and perfect audio
    balance. You create content optimized for social media platforms with proper aspect ratios and timing.""",
    tools=[video_production_tool],
    verbose=True
)

class BuddhaQuoteCrew:
    def __init__(self):
        self.crew = None

    def create_crew(self):
        # Define Tasks
        research_task = Task(
            description="""Research current trending topics and hashtags related to Buddha quotes,
            mindfulness, motivation, and spiritual growth. Use both Google Trends and social media
            trends to find topics that would resonate with Instagram audiences. Include relevant
            hashtags that are currently trending.""",
            agent=content_researcher,
            expected_output="A comprehensive report with 3-5 trending topics and 5-10 relevant hashtags for spiritual content"
        )

        quote_task = Task(
            description="""Based on the trending topics, generate an original Buddha-style
            motivational quote. The quote should be under 150 characters and encourage
            peace and mindfulness.""",
            agent=quote_creator,
            expected_output="A meaningful Buddha-style quote under 150 characters"
        )

        # New Enhanced Tasks
        image_generation_task = Task(
            description="""Create a visually stunning image for the Buddha quote using AI image generation.
            Extract specific visual parameters from the quote content including:
            - Image style: (serene, minimalist, nature-inspired, golden hour, etc.)
            - Dimensions: 1080x1920 (Instagram Story/Reel format)
            - Color scheme: (warm earth tones, golden sunset, peaceful blues, etc.)
            - Mood: (peaceful, contemplative, inspiring, calming)
            - Subject matter: (Buddha silhouette, lotus flowers, mountain landscapes, zen gardens)
            - Composition: (centered, rule of thirds, symmetrical, etc.)

            Generate an image that perfectly complements the spiritual message and creates visual harmony
            with the quote text overlay.""",
            agent=image_generation_agent,
            expected_output="Path to generated image file with detailed parameters used for creation"
        )

        voice_synthesis_task = Task(
            description="""Generate a high-quality voiceover for the Buddha quote using ElevenLabs AI.
            Analyze the quote text to extract optimal voice parameters:
            - Speech pace: (slow and contemplative, moderate, or gentle rhythm based on quote length)
            - Emotional tone: (wise and warm, peaceful and calming, inspiring and uplifting)
            - Emphasis points: (identify key words or phrases that need vocal emphasis)
            - Pause locations: (natural breathing points, dramatic pauses for impact)
            - Voice characteristics: Use "Grandpa Spuds Oxley" voice model for warm, wise delivery

            Create a voiceover that brings the spiritual wisdom to life with perfect pacing and emotion.""",
            agent=voice_synthesis_agent,
            expected_output="Path to generated audio file with timing and emphasis details"
        )

        music_selection_task = Task(
            description="""Select perfect royalty-free background music from Pixabay for the inspirational video.
            Extract specific music parameters based on the quote's mood and message:
            - Music genre: (ambient, cinematic, meditation, soft instrumental, nature sounds)
            - Tempo: (slow 60-80 BPM for contemplative, moderate 80-100 BPM for uplifting)
            - Mood: (peaceful, inspiring, mystical, uplifting, contemplative)
            - Duration: Match video length (15-30 seconds) with smooth fade in/out
            - Volume levels: Background level that doesn't overpower voiceover (20-30% of voice volume)

            Select music that creates emotional resonance and enhances the spiritual message.""",
            agent=music_selection_agent,
            expected_output="Path to selected music file with timing and volume specifications"
        )

        video_production_task = Task(
            description="""Create a professional cinematic video combining the AI-generated image, voiceover, and background music.
            Extract and implement specific production parameters:
            - Video length: Optimize based on voiceover duration (typically 15-30 seconds)
            - Transition effects: Smooth fade-in (0.5s), crossfade between elements, fade-out (0.5s)
            - Text overlay timing: Sync quote text appearance with voiceover pacing
            - Audio synchronization: Perfect balance between voice and background music
            - Visual effects: Subtle zoom, parallax, or breathing effects on background image
            - Aspect ratio: 9:16 vertical format for Instagram Reels/Stories
            - Quality: 1080p resolution with smooth 30fps playback

            Create a cohesive, engaging video that combines all elements into professional social media content.""",
            agent=video_production_agent,
            expected_output="Path to final produced video file with technical specifications"
        )

        posting_task = Task(
            description="""Post the professionally produced video to Instagram with an engaging caption
            and relevant hashtags to maximize reach and engagement.""",
            agent=social_media_manager,
            expected_output="Confirmation of successful Instagram post"
        )

        # Create Enhanced Crew with all specialized agents
        self.crew = Crew(
            agents=[
                content_researcher,
                quote_creator,
                image_generation_agent,
                voice_synthesis_agent,
                music_selection_agent,
                video_production_agent,
                social_media_manager
            ],
            tasks=[
                research_task,
                quote_task,
                image_generation_task,
                voice_synthesis_task,
                music_selection_task,
                video_production_task,
                posting_task
            ],
            process=Process.sequential,
            verbose=True
        )

        return self.crew

    def run_pipeline(self):
        if not self.crew:
            self.create_crew()

        result = self.crew.kickoff()
        return result