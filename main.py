import schedule
import time
import os
import sys
import argparse
from datetime import datetime
from agents.crew_agents import Buddha<PERSON><PERSON><PERSON><PERSON><PERSON>
from tools.content_tools import TrendResearchTool, QuoteGeneratorTool
from tools.video_tools import VideoCreatorTool
from tools.instagram_tools import InstagramPostTool
from instagrapi import Client
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class BuddhaBot:
    def __init__(self, args=None):
        self.args = args or self.parse_arguments()
        self.setup_logging()
        self.environment = self.detect_environment()
        self.components_verified = False

    def parse_arguments(self):
        """Parse command line arguments"""
        parser = argparse.ArgumentParser(description='Buddha Quotes Instagram Bot - Enhanced Edition')
        parser.add_argument('--test', action='store_true',
                          help='Run a single test execution and exit')
        parser.add_argument('--production', action='store_true',
                          help='Force production mode (scheduled execution)')
        parser.add_argument('--development', action='store_true',
                          help='Force development mode (test then schedule)')
        parser.add_argument('--verify-only', action='store_true',
                          help='Only verify components and exit')
        parser.add_argument('--schedule-only', action='store_true',
                          help='Skip test run and go directly to scheduling')
        parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                          default='INFO', help='Set logging level')
        return parser.parse_args()

    def setup_logging(self):
        """Setup comprehensive logging system"""
        os.makedirs("logs", exist_ok=True)

        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )

        # Setup root logger with configurable level
        self.logger = logging.getLogger('BuddhaBot')
        log_level = getattr(logging, self.args.log_level if hasattr(self, 'args') else 'INFO')
        self.logger.setLevel(log_level)

        # File handler for detailed logs
        file_handler = logging.FileHandler('logs/buddha_bot.log')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)

        # Console handler for immediate feedback with UTF-8 encoding
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)

        # Set UTF-8 encoding for console output on Windows
        if sys.platform.startswith('win'):
            import codecs
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')

        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

        # Prevent duplicate logs
        self.logger.propagate = False

    def detect_environment(self):
        """Detect if running in development or production mode"""
        # Check command line arguments first
        if hasattr(self, 'args'):
            if self.args.production:
                environment = 'production'
                self.logger.info(f"[INFO] Environment forced to PRODUCTION via --production flag")
                return environment
            elif self.args.development:
                environment = 'development'
                self.logger.info(f"[INFO] Environment forced to DEVELOPMENT via --development flag")
                return environment

        # Check for common production environment indicators
        is_production = any([
            os.getenv('ENVIRONMENT') == 'production',
            os.getenv('PROD') == 'true',
            os.path.exists('/.dockerenv'),  # Running in Docker
            os.getenv('RAILWAY_ENVIRONMENT'),  # Railway deployment
            os.getenv('HEROKU_APP_NAME'),  # Heroku deployment
            '--production' in sys.argv
        ])

        environment = 'production' if is_production else 'development'
        self.logger.info(f"[INFO] Environment detected: {environment.upper()}")
        return environment

    def verify_components(self):
        """Verify all system components are properly initialized"""
        self.logger.info("[SETUP] Verifying system components...")

        verification_results = {
            'environment_vars': False,
            'directories': False,
            'tools': False,
            'instagram_connection': False,
            'crewai_agents': False
        }

        try:
            # 1. Verify environment variables
            self.logger.info("   [CHECK] Checking environment variables...")
            required_vars = ['INSTA_USERNAME', 'INSTA_PASSWORD']
            missing_vars = [var for var in required_vars if not os.getenv(var)]

            if missing_vars:
                self.logger.warning(f"   [WARN] Missing environment variables: {missing_vars}")
                if self.environment == 'development':
                    self.logger.info("   [INFO] Running in development mode - some features may be limited")
                verification_results['environment_vars'] = len(missing_vars) == 0
            else:
                self.logger.info("   [OK] Environment variables configured")
                verification_results['environment_vars'] = True

            # 2. Verify directories
            self.logger.info("   [CHECK] Checking required directories...")
            required_dirs = ['logs', 'temp', 'assets', 'assets/backgrounds']
            for directory in required_dirs:
                os.makedirs(directory, exist_ok=True)
            self.logger.info("   [OK] Directory structure verified")
            verification_results['directories'] = True

            # 3. Verify CrewAI tools
            self.logger.info("   [CHECK] Initializing CrewAI tools...")
            try:
                trend_tool = TrendResearchTool()
                quote_tool = QuoteGeneratorTool()
                video_tool = VideoCreatorTool()
                instagram_tool = InstagramPostTool()
                self.logger.info("   [OK] CrewAI tools initialized successfully")
                verification_results['tools'] = True
            except Exception as e:
                self.logger.error(f"   [ERROR] CrewAI tools initialization failed: {str(e)}")
                verification_results['tools'] = False

            # 4. Verify Instagram connection (only if credentials available)
            if verification_results['environment_vars']:
                self.logger.info("   [CHECK] Testing Instagram connection...")
                try:
                    # Just check if credentials exist, don't actually connect
                    username = os.getenv('INSTA_USERNAME')
                    password = os.getenv('INSTA_PASSWORD')
                    if username and password and username != 'your_instagram_username':
                        self.logger.info(f"   [OK] Instagram credentials configured for: {username}")
                        verification_results['instagram_connection'] = True
                    else:
                        self.logger.warning("   [WARN] Instagram username not configured")
                        verification_results['instagram_connection'] = False
                except Exception as e:
                    self.logger.warning(f"   [WARN] Instagram connection test failed: {str(e)}")
                    verification_results['instagram_connection'] = False

            # 5. Verify CrewAI agents (Enhanced Edition)
            self.logger.info("   [CHECK] Initializing Enhanced CrewAI agents...")
            try:
                buddha_crew = BuddhaQuoteCrew()
                crew = buddha_crew.create_crew()
                expected_agents = 7  # Updated for enhanced edition with specialized agents
                if crew and len(crew.agents) == expected_agents:
                    self.logger.info(f"   [OK] Enhanced CrewAI agents initialized ({expected_agents} agents ready)")
                    self.logger.info("   [INFO] Agents: Content Research, Quote Creation, Image Generation, Voice Synthesis, Music Selection, Video Production, Social Media Management")
                    verification_results['crewai_agents'] = True
                elif crew and len(crew.agents) >= 4:
                    self.logger.warning(f"   [WARN] Partial agent initialization: {len(crew.agents)}/{expected_agents} agents ready")
                    verification_results['crewai_agents'] = True  # Still functional with core agents
                else:
                    self.logger.error(f"   [ERROR] Insufficient agents initialized: {len(crew.agents) if crew else 0}/{expected_agents}")
                    verification_results['crewai_agents'] = False
            except Exception as e:
                self.logger.error(f"   [ERROR] Enhanced CrewAI agents initialization failed: {str(e)}")
                self.logger.error(f"   [DEBUG] Error type: {type(e).__name__}")
                verification_results['crewai_agents'] = False

        except Exception as e:
            self.logger.error(f"Component verification failed: {str(e)}")

        # Summary
        verified_count = sum(verification_results.values())
        total_count = len(verification_results)

        self.logger.info(f"🔍 Component verification complete: {verified_count}/{total_count} components ready")

        if verified_count == total_count:
            self.logger.info("✅ All components verified successfully!")
            self.components_verified = True
        elif verified_count >= 3:  # Minimum viable components
            self.logger.warning("⚠️  Some components have issues but system can proceed")
            self.components_verified = True
        else:
            self.logger.error("❌ Critical components failed - system may not function properly")
            self.components_verified = False

        return verification_results

    def run_content_pipeline(self, is_test_run=False):
        """Execute the CrewAI content creation pipeline"""
        run_type = "TEST RUN" if is_test_run else "SCHEDULED RUN"
        self.logger.info(f"🚀 Starting {run_type} at {datetime.now()}")

        try:
            # Initialize CrewAI
            self.logger.info("   🤖 Initializing Buddha Quote CrewAI...")
            buddha_crew = BuddhaQuoteCrew()

            # Execute pipeline
            self.logger.info("   ⚙️  Executing CrewAI pipeline...")
            result = buddha_crew.run_pipeline()

            self.logger.info(f"✅ {run_type} completed successfully!")
            self.logger.info(f"   📊 Result: {result}")

            return True, result

        except Exception as e:
            self.logger.error(f"❌ {run_type} failed: {str(e)}")
            self.logger.error(f"   🔍 Error details: {type(e).__name__}")
            return False, str(e)

    def run_immediate_test(self):
        """Run immediate test for development verification"""
        self.logger.info("🧪 IMMEDIATE TESTING MODE ACTIVATED")
        self.logger.info("=" * 50)

        if not self.components_verified:
            self.logger.warning("⚠️  Components not fully verified - test may fail")

        success, result = self.run_content_pipeline(is_test_run=True)

        if success:
            self.logger.info("🎉 Test run successful! System is ready for production.")
        else:
            self.logger.error("💥 Test run failed! Check logs for details.")
            self.logger.info("🔄 Will still proceed to start scheduler for production mode...")

        self.logger.info("=" * 50)
        return success

    def schedule_production_runs(self):
        """Setup production scheduling with timezone awareness"""
        self.logger.info("📅 PRODUCTION SCHEDULING MODE ACTIVATED")

        # Get scheduling times from environment or use defaults
        posting_times = os.getenv('POSTING_TIMES', '08:00,14:00,20:00').split(',')

        # Schedule posts at specified times (assumes system timezone is IST or properly configured)
        for time_str in posting_times:
            time_str = time_str.strip()
            try:
                schedule.every().day.at(time_str).do(self._scheduled_run_wrapper)
                self.logger.info(f"   ✅ Scheduled post for {time_str}")
            except Exception as e:
                self.logger.error(f"   ❌ Failed to schedule {time_str}: {e}")

        self.logger.info("⏰ Production schedule configured:")
        for time_str in posting_times:
            time_str = time_str.strip()
            self.logger.info(f"   • {time_str} (System Timezone)")

        self.logger.info("💡 Note: Ensure system timezone is set to IST for correct scheduling")

        # Log next scheduled run
        next_run = schedule.next_run()
        if next_run:
            self.logger.info(f"🕐 Next scheduled run: {next_run}")

    def _scheduled_run_wrapper(self):
        """Wrapper for scheduled runs with error handling"""
        try:
            success, result = self.run_content_pipeline(is_test_run=False)
            if not success:
                self.logger.error("📱 Scheduled run failed - will retry at next scheduled time")
        except Exception as e:
            self.logger.error(f"📱 Scheduled run error: {str(e)}")
            # Don't crash the scheduler, just log and continue

    def run_scheduler_loop(self):
        """Run the main scheduler loop with enhanced production features"""
        self.logger.info("🔄 SCHEDULER LOOP STARTED")
        self.logger.info("   💡 Press Ctrl+C to stop gracefully")

        # Show current time and timezone info
        current_time = datetime.now()
        self.logger.info(f"🕐 Current system time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

        # Show next scheduled runs
        jobs = schedule.get_jobs()
        if jobs:
            self.logger.info(f"📋 {len(jobs)} scheduled jobs:")
            for job in jobs:
                self.logger.info(f"   • Next run: {job.next_run}")

        loop_count = 0
        try:
            while True:
                schedule.run_pending()

                # Log heartbeat every hour in production
                if loop_count % 60 == 0:  # Every 60 minutes
                    self.logger.info(f"💓 Scheduler heartbeat - {datetime.now().strftime('%H:%M:%S')}")
                    if jobs:
                        next_job = min(jobs, key=lambda x: x.next_run)
                        self.logger.info(f"🕐 Next scheduled run: {next_job.next_run}")

                time.sleep(60)  # Check every minute
                loop_count += 1

        except KeyboardInterrupt:
            self.logger.info("🛑 Scheduler stopped gracefully by user")
        except Exception as e:
            self.logger.error(f"💥 Scheduler error: {str(e)}")
            self.logger.error("🔄 Attempting to restart scheduler in 5 minutes...")
            time.sleep(300)  # Wait 5 minutes before potential restart
            # Don't use recursive restart to avoid stack overflow
            self.logger.info("🔄 Restarting scheduler...")
            self.run_scheduler_loop()

    def run(self):
        """Main execution method with enhanced command line support"""
        self.logger.info("🌟 BUDDHA QUOTES INSTAGRAM BOT - ENHANCED EDITION")
        self.logger.info("=" * 70)

        # Show current configuration
        self.logger.info(f"🔧 Configuration:")
        self.logger.info(f"   • Environment: {self.environment.upper()}")
        self.logger.info(f"   • Log Level: {self.args.log_level}")
        if hasattr(self.args, 'test') and self.args.test:
            self.logger.info(f"   • Mode: SINGLE TEST RUN")
        elif hasattr(self.args, 'verify_only') and self.args.verify_only:
            self.logger.info(f"   • Mode: VERIFICATION ONLY")
        elif hasattr(self.args, 'schedule_only') and self.args.schedule_only:
            self.logger.info(f"   • Mode: DIRECT TO SCHEDULING")
        else:
            self.logger.info(f"   • Mode: FULL OPERATION")

        # Verify components
        verification_results = self.verify_components()

        # Handle different execution modes
        if hasattr(self.args, 'verify_only') and self.args.verify_only:
            self.logger.info("🔍 VERIFICATION ONLY MODE - Exiting after component check")
            return

        if hasattr(self.args, 'test') and self.args.test:
            self.logger.info("🧪 SINGLE TEST MODE - Running one test and exiting")
            success, result = self.run_content_pipeline(is_test_run=True)
            if success:
                self.logger.info("🎉 Single test completed successfully!")
                sys.exit(0)
            else:
                self.logger.error("💥 Single test failed!")
                sys.exit(1)

        # Development mode behavior
        if self.environment == 'development' and not (hasattr(self.args, 'schedule_only') and self.args.schedule_only):
            self.logger.info("🧪 Development mode: Running immediate test...")
            test_success = self.run_immediate_test()

            if test_success:
                self.logger.info("✅ Test successful! Proceeding to production scheduling...")
            else:
                self.logger.warning("⚠️  Test failed but continuing to production scheduling...")

        # Setup production scheduling
        self.schedule_production_runs()

        # Start scheduler loop
        self.run_scheduler_loop()

def main():
    """Entry point with enhanced command line support"""
    try:
        # Parse arguments first
        parser = argparse.ArgumentParser(description='Buddha Quotes Instagram Bot - Enhanced Edition')
        parser.add_argument('--test', action='store_true',
                          help='Run a single test execution and exit')
        parser.add_argument('--production', action='store_true',
                          help='Force production mode (scheduled execution)')
        parser.add_argument('--development', action='store_true',
                          help='Force development mode (test then schedule)')
        parser.add_argument('--verify-only', action='store_true',
                          help='Only verify components and exit')
        parser.add_argument('--schedule-only', action='store_true',
                          help='Skip test run and go directly to scheduling')
        parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                          default='INFO', help='Set logging level')

        args = parser.parse_args()

        # Show help information
        print("🌟 Buddha Quotes Instagram Bot - Enhanced Edition")
        print("=" * 60)
        print("Available modes:")
        print("  python main.py                    # Full operation (test + schedule)")
        print("  python main.py --test             # Single test run only")
        print("  python main.py --verify-only      # Component verification only")
        print("  python main.py --schedule-only    # Skip test, go to scheduling")
        print("  python main.py --production       # Force production mode")
        print("  python main.py --development      # Force development mode")
        print("  python main.py --log-level DEBUG  # Set log level")
        print("=" * 60)

        # Initialize and run bot
        bot = BuddhaBot(args)
        bot.run()

    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"💥 Critical error starting bot: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
