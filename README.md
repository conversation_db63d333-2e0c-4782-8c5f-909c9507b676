# <PERSON> Quotes Instagram Bot with CrewAI - Enhanced Edition

An advanced autonomous multi-agent system built with CrewAI that creates and posts professional-quality Buddha-inspired motivational content to Instagram using specialized AI agents for comprehensive content creation.

## 🤖 Enhanced CrewAI Agents

### Core Content Agents
- **Content Researcher**: Analyzes trending topics using Google Trends and social media APIs
- **Quote Creator**: Generates authentic Buddha-style quotes with spiritual wisdom

### Specialized Production Agents
- **Image Generation Agent**: Creates stunning AI-generated images using Imagine.art API
  - Extracts visual parameters: style, dimensions, color scheme, mood, subject matter, composition
  - Integrates with Imagine.art, Unsplash, and Pixabay APIs
  - Creates images perfectly suited for quote overlays

- **Voice Synthesis Agent**: Generates high-quality voiceovers using ElevenLabs AI
  - Uses "Grandpa Spuds Oxley" voice model for warm, wise delivery
  - Extracts speech parameters: pace, emotional tone, emphasis points, pause locations
  - Supports multiple TTS providers (ElevenLabs, Google Cloud, Azure)

- **Music Selection Agent**: Curates perfect background music from Pixabay
  - Extracts music parameters: genre, tempo, mood, duration, volume levels
  - Selects soft, ambient music suitable for inspirational content
  - Provides royalty-free music with proper licensing

- **Video Production Agent**: Creates cinematic videos with professional effects
  - Combines AI images, voiceovers, and background music
  - Applies transition effects, text overlays, and audio synchronization
  - Produces 9:16 vertical videos optimized for Instagram Reels

### Content Management
- **Social Media Manager**: Posts content to Instagram with optimal engagement strategies

## 🚀 Quick Start

1. **Setup Project**
   ```bash
   python setup.py
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your credentials
   ```

3. **Run the Bot**
   ```bash
   python main.py
   ```

## 🛠️ Enhanced CrewAI Tools

### Content Research & Generation
- **TrendResearchTool**: Multi-source trend analysis (Google Trends, Reddit, HackerNews)
- **QuoteGeneratorTool**: Buddha-style quote generation with multiple API fallbacks
- **SocialMediaTrendsTool**: Hashtag and trend analysis for optimal engagement

### Media Production Tools
- **ImageGenerationTool**: AI image creation with Imagine.art, Unsplash, and Pixabay integration
- **VoiceSynthesisTool**: Professional voiceover generation with ElevenLabs and alternatives
- **MusicSelectionTool**: Royalty-free music curation from Pixabay with mood matching
- **VideoProductionTool**: Professional video editing with MoviePy, OpenCV, and NumPy

### Publishing & Management
- **InstagramPostTool**: Advanced Instagram posting with engagement optimization

## 🔧 API Integrations

### Required APIs
- **OpenAI**: For CrewAI agent coordination
- **ElevenLabs**: Voice synthesis with "Grandpa Spuds Oxley" voice

### Optional APIs (with fallbacks)
- **Imagine.art**: AI image generation
- **Pixabay**: Royalty-free images and music
- **Unsplash**: High-quality stock photography
- **Google Cloud TTS**: Alternative voice synthesis
- **Azure Cognitive Services**: Alternative voice synthesis
- **Freesound.org**: Additional audio resources

## 📅 Scheduling

Posts automatically at:
- 8:00 AM IST
- 2:00 PM IST
- 8:00 PM IST

## 🐳 Docker Deployment

```bash
docker build -t buddha-bot .
docker run -d --env-file .env buddha-bot
```

## 🔧 Configuration

Update `config/settings.py` for custom configurations and `tools/` directory for custom CrewAI tools.