#!/usr/bin/env python3
"""
Production Deployment Script for <PERSON> Quotes Instagram Bot
Handles production setup, environment validation, and deployment
"""

import os
import sys
import subprocess
import json
from datetime import datetime
from pathlib import Path

class ProductionDeployer:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.log_file = self.project_root / "logs" / f"deployment_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
    def log(self, message, level="INFO"):
        """Log message to console and file"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] [{level}] {message}"
        print(log_entry)
        
        # Ensure logs directory exists
        self.log_file.parent.mkdir(exist_ok=True)
        
        # Write to log file
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
    
    def check_python_version(self):
        """Check Python version compatibility"""
        self.log("Checking Python version...")
        
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            self.log(f"ERROR: Python 3.8+ required, found {version.major}.{version.minor}", "ERROR")
            return False
        
        self.log(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    
    def validate_environment(self):
        """Validate production environment variables"""
        self.log("Validating production environment...")
        
        required_vars = {
            'OPENAI_API_KEY': 'OpenAI API for CrewAI agents',
            'INSTA_USERNAME': 'Instagram username for posting',
            'INSTA_PASSWORD': 'Instagram password for posting',
            'ELEVENLABS_API_KEY': 'ElevenLabs API for voice synthesis'
        }
        
        optional_vars = {
            'IMAGINE_ART_API_KEY': 'Imagine.art API for image generation',
            'PIXABAY_API_KEY': 'Pixabay API for images and music',
            'UNSPLASH_ACCESS_KEY': 'Unsplash API for stock photos',
            'GOOGLE_CLOUD_TTS_KEY': 'Google Cloud TTS (alternative voice)',
            'AZURE_SPEECH_KEY': 'Azure Speech Services (alternative voice)',
            'FREESOUND_API_KEY': 'Freesound.org API for audio',
            'NEWSAPI_KEY': 'NewsAPI for trend research',
            'RAPIDAPI_KEY': 'RapidAPI for additional services'
        }
        
        missing_required = []
        missing_optional = []
        
        # Check required variables
        for var, description in required_vars.items():
            value = os.getenv(var)
            if value and value.strip() and not value.startswith('your_'):
                self.log(f"✅ {var}: Configured")
            else:
                self.log(f"❌ {var}: Missing or not configured - {description}", "ERROR")
                missing_required.append(var)
        
        # Check optional variables
        for var, description in optional_vars.items():
            value = os.getenv(var)
            if value and value.strip() and not value.startswith('your_'):
                self.log(f"✅ {var}: Configured")
            else:
                self.log(f"⚠️  {var}: Not configured - {description} (fallback available)", "WARNING")
                missing_optional.append(var)
        
        if missing_required:
            self.log(f"CRITICAL: Missing required environment variables: {', '.join(missing_required)}", "ERROR")
            return False
        
        if missing_optional:
            self.log(f"INFO: {len(missing_optional)} optional APIs not configured - enhanced features will use fallbacks")
        
        return True
    
    def install_dependencies(self):
        """Install production dependencies"""
        self.log("Installing production dependencies...")
        
        try:
            # Install core requirements
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt', '--upgrade'
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                self.log("✅ Dependencies installed successfully")
                return True
            else:
                self.log(f"❌ Dependency installation failed: {result.stderr}", "ERROR")
                return False
                
        except subprocess.TimeoutExpired:
            self.log("❌ Dependency installation timed out", "ERROR")
            return False
        except Exception as e:
            self.log(f"❌ Dependency installation error: {e}", "ERROR")
            return False
    
    def verify_components(self):
        """Verify all bot components"""
        self.log("Verifying bot components...")
        
        try:
            # Run component verification
            result = subprocess.run([
                sys.executable, 'main.py', '--verify-only', '--log-level', 'WARNING'
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                self.log("✅ All components verified successfully")
                return True
            else:
                self.log(f"❌ Component verification failed", "ERROR")
                self.log(f"STDOUT: {result.stdout}", "DEBUG")
                self.log(f"STDERR: {result.stderr}", "DEBUG")
                return False
                
        except subprocess.TimeoutExpired:
            self.log("❌ Component verification timed out", "ERROR")
            return False
        except Exception as e:
            self.log(f"❌ Component verification error: {e}", "ERROR")
            return False
    
    def create_systemd_service(self):
        """Create systemd service file for Linux production deployment"""
        if os.name != 'posix':
            self.log("Systemd service creation skipped (not on Linux)")
            return True
        
        self.log("Creating systemd service file...")
        
        service_content = f"""[Unit]
Description=Buddha Quotes Instagram Bot - Enhanced Edition
After=network.target
Wants=network.target

[Service]
Type=simple
User={os.getenv('USER', 'buddha-bot')}
WorkingDirectory={self.project_root}
Environment=PYTHONPATH={self.project_root}
ExecStart={sys.executable} {self.project_root}/main.py --production --log-level INFO
Restart=always
RestartSec=30
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
"""
        
        service_file = self.project_root / "buddha-bot.service"
        try:
            with open(service_file, 'w') as f:
                f.write(service_content)
            
            self.log(f"✅ Systemd service file created: {service_file}")
            self.log("To install the service:")
            self.log(f"  sudo cp {service_file} /etc/systemd/system/")
            self.log("  sudo systemctl daemon-reload")
            self.log("  sudo systemctl enable buddha-bot.service")
            self.log("  sudo systemctl start buddha-bot.service")
            return True
            
        except Exception as e:
            self.log(f"❌ Failed to create systemd service: {e}", "ERROR")
            return False
    
    def create_docker_files(self):
        """Create Docker deployment files"""
        self.log("Creating Docker deployment files...")
        
        # Dockerfile
        dockerfile_content = """FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    ffmpeg \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p logs temp assets

# Set timezone to IST
ENV TZ=Asia/Kolkata
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Run the bot in production mode
CMD ["python", "main.py", "--production", "--log-level", "INFO"]
"""
        
        # Docker Compose
        compose_content = """version: '3.8'

services:
  buddha-bot:
    build: .
    container_name: buddha-quotes-bot
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - TZ=Asia/Kolkata
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./temp:/app/temp
      - ./assets:/app/assets
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
"""
        
        try:
            # Write Dockerfile
            with open(self.project_root / "Dockerfile", 'w') as f:
                f.write(dockerfile_content)
            
            # Write docker-compose.yml
            with open(self.project_root / "docker-compose.yml", 'w') as f:
                f.write(compose_content)
            
            self.log("✅ Docker files created successfully")
            self.log("To deploy with Docker:")
            self.log("  docker-compose up -d")
            return True
            
        except Exception as e:
            self.log(f"❌ Failed to create Docker files: {e}", "ERROR")
            return False
    
    def run_production_test(self):
        """Run a production test"""
        self.log("Running production test...")
        
        try:
            result = subprocess.run([
                sys.executable, 'main.py', '--test', '--log-level', 'INFO'
            ], timeout=300)
            
            if result.returncode == 0:
                self.log("✅ Production test completed successfully")
                return True
            else:
                self.log("❌ Production test failed", "ERROR")
                return False
                
        except subprocess.TimeoutExpired:
            self.log("❌ Production test timed out", "ERROR")
            return False
        except Exception as e:
            self.log(f"❌ Production test error: {e}", "ERROR")
            return False
    
    def deploy(self):
        """Main deployment process"""
        self.log("🚀 STARTING PRODUCTION DEPLOYMENT")
        self.log("=" * 50)
        
        steps = [
            ("Python Version Check", self.check_python_version),
            ("Environment Validation", self.validate_environment),
            ("Dependency Installation", self.install_dependencies),
            ("Component Verification", self.verify_components),
            ("Production Test", self.run_production_test),
            ("Systemd Service Creation", self.create_systemd_service),
            ("Docker Files Creation", self.create_docker_files),
        ]
        
        failed_steps = []
        
        for step_name, step_func in steps:
            self.log(f"📋 {step_name}...")
            try:
                if not step_func():
                    failed_steps.append(step_name)
                    self.log(f"❌ {step_name} FAILED", "ERROR")
                else:
                    self.log(f"✅ {step_name} COMPLETED")
            except Exception as e:
                failed_steps.append(step_name)
                self.log(f"❌ {step_name} ERROR: {e}", "ERROR")
        
        self.log("=" * 50)
        if failed_steps:
            self.log(f"💥 DEPLOYMENT FAILED - {len(failed_steps)} steps failed:", "ERROR")
            for step in failed_steps:
                self.log(f"   • {step}", "ERROR")
            self.log(f"📋 Check deployment log: {self.log_file}")
            return False
        else:
            self.log("🎉 DEPLOYMENT SUCCESSFUL!")
            self.log("🚀 Ready for production!")
            self.log(f"📋 Deployment log: {self.log_file}")
            return True

def main():
    """Main deployment entry point"""
    print("🌟 Buddha Quotes Instagram Bot - Production Deployment")
    print("=" * 60)
    
    deployer = ProductionDeployer()
    success = deployer.deploy()
    
    if success:
        print("\n🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!")
        print("🚀 Your bot is ready for production!")
        print("\nNext steps:")
        print("1. Review the deployment log for any warnings")
        print("2. Start the bot: python main.py --production")
        print("3. Or use Docker: docker-compose up -d")
        print("4. Monitor logs: tail -f logs/buddha_bot.log")
        sys.exit(0)
    else:
        print("\n💥 DEPLOYMENT FAILED!")
        print("❌ Please check the deployment log and fix the issues")
        sys.exit(1)

if __name__ == "__main__":
    main()
