from crewai.tools import BaseTool
from typing import Type
from pydantic import BaseModel, Field
import requests
import os
import time
import json
import base64
from PIL import Image
import io

class ImageGenerationInput(BaseModel):
    quote: str = Field(description="Quote text to generate image for")
    style: str = Field(default="serene", description="Image style (serene, minimalist, nature-inspired, golden hour)")
    dimensions: str = Field(default="1080x1920", description="Image dimensions")
    color_scheme: str = Field(default="warm earth tones", description="Color scheme for the image")
    mood: str = Field(default="peaceful", description="Mood of the image")
    subject_matter: str = Field(default="Buddha silhouette", description="Main subject matter")
    composition: str = Field(default="centered", description="Image composition style")

class ImageGenerationTool(BaseTool):
    name: str = "Image Generation Tool"
    description: str = "Generates AI images using Imagine.art API for social media posts"
    args_schema: Type[BaseModel] = ImageGenerationInput

    def _run(self, quote: str, style: str = "serene", dimensions: str = "1080x1920", 
             color_scheme: str = "warm earth tones", mood: str = "peaceful", 
             subject_matter: str = "Buddha silhouette", composition: str = "centered") -> str:
        try:
            # Ensure temp directory exists
            os.makedirs("temp", exist_ok=True)
            
            # Extract parameters from quote content for better image generation
            extracted_params = self._extract_visual_parameters(quote, style, color_scheme, mood, subject_matter, composition)
            
            # Try Imagine.art API (if available)
            imagine_result = self._try_imagine_art_api(extracted_params, dimensions)
            if imagine_result:
                return imagine_result
            
            # Try alternative free image generation APIs
            alternative_result = self._try_alternative_apis(extracted_params, dimensions)
            if alternative_result:
                return alternative_result
            
            # Fallback: Create a simple gradient background with PIL
            fallback_result = self._create_fallback_image(extracted_params, dimensions)
            return fallback_result
            
        except Exception as e:
            return f"Error in image generation: {str(e)}"

    def _extract_visual_parameters(self, quote: str, style: str, color_scheme: str, 
                                 mood: str, subject_matter: str, composition: str) -> dict:
        """Extract and enhance visual parameters based on quote content"""
        
        # Analyze quote for visual cues
        quote_lower = quote.lower()
        
        # Enhanced style based on quote content
        if any(word in quote_lower for word in ['peace', 'calm', 'tranquil']):
            enhanced_style = "serene minimalist zen"
        elif any(word in quote_lower for word in ['light', 'sun', 'dawn', 'bright']):
            enhanced_style = "golden hour luminous"
        elif any(word in quote_lower for word in ['nature', 'tree', 'mountain', 'water']):
            enhanced_style = "nature-inspired landscape"
        elif any(word in quote_lower for word in ['wisdom', 'ancient', 'temple']):
            enhanced_style = "mystical ancient wisdom"
        else:
            enhanced_style = style
        
        # Enhanced color scheme
        if any(word in quote_lower for word in ['gold', 'sun', 'light']):
            enhanced_colors = "golden sunset warm amber"
        elif any(word in quote_lower for word in ['blue', 'sky', 'ocean', 'water']):
            enhanced_colors = "peaceful blues soft cyan"
        elif any(word in quote_lower for word in ['green', 'nature', 'forest']):
            enhanced_colors = "natural greens earth tones"
        else:
            enhanced_colors = color_scheme
        
        # Enhanced subject matter
        if any(word in quote_lower for word in ['lotus', 'flower']):
            enhanced_subject = "lotus flower meditation"
        elif any(word in quote_lower for word in ['mountain', 'peak']):
            enhanced_subject = "mountain landscape zen"
        elif any(word in quote_lower for word in ['water', 'river', 'ocean']):
            enhanced_subject = "peaceful water reflection"
        elif any(word in quote_lower for word in ['tree', 'forest']):
            enhanced_subject = "ancient tree wisdom"
        else:
            enhanced_subject = subject_matter
        
        return {
            'style': enhanced_style,
            'color_scheme': enhanced_colors,
            'mood': mood,
            'subject_matter': enhanced_subject,
            'composition': composition,
            'quote_essence': quote_lower
        }

    def _try_imagine_art_api(self, params: dict, dimensions: str) -> str:
        """Try Imagine.art API for image generation"""
        try:
            # Note: This would require actual Imagine.art API integration
            # For now, we'll simulate the API call structure
            
            imagine_api_key = os.getenv('IMAGINE_ART_API_KEY')
            if not imagine_api_key:
                return None
            
            # Construct detailed prompt
            prompt = f"Create a {params['style']} image with {params['color_scheme']} colors, " \
                    f"featuring {params['subject_matter']} in a {params['composition']} composition, " \
                    f"conveying a {params['mood']} mood, perfect for spiritual quote overlay, " \
                    f"high quality, cinematic lighting, peaceful atmosphere"
            
            # API call structure (would need actual implementation)
            url = "https://api.imagine.art/v1/generate"  # Hypothetical endpoint
            headers = {
                "Authorization": f"Bearer {imagine_api_key}",
                "Content-Type": "application/json"
            }
            
            width, height = dimensions.split('x')
            payload = {
                "prompt": prompt,
                "width": int(width),
                "height": int(height),
                "style": params['style'],
                "quality": "high"
            }
            
            # This would be the actual API call
            # response = requests.post(url, headers=headers, json=payload, timeout=30)
            # For now, return None to trigger fallback
            return None
            
        except Exception as e:
            print(f"Imagine.art API error: {e}")
            return None

    def _try_alternative_apis(self, params: dict, dimensions: str) -> str:
        """Try alternative free image generation APIs"""
        try:
            # Try Unsplash API for stock photos
            unsplash_result = self._try_unsplash_api(params)
            if unsplash_result:
                return unsplash_result
            
            # Try Pixabay API for free images
            pixabay_result = self._try_pixabay_api(params)
            if pixabay_result:
                return pixabay_result
            
            return None
            
        except Exception as e:
            print(f"Alternative API error: {e}")
            return None

    def _try_unsplash_api(self, params: dict) -> str:
        """Try Unsplash API for relevant stock photos"""
        try:
            unsplash_key = os.getenv('UNSPLASH_ACCESS_KEY')
            if not unsplash_key:
                return None
            
            # Search for relevant images
            search_terms = f"{params['subject_matter']} {params['mood']} meditation zen"
            url = f"https://api.unsplash.com/search/photos"
            headers = {"Authorization": f"Client-ID {unsplash_key}"}
            params_dict = {
                "query": search_terms,
                "orientation": "portrait",
                "per_page": 5
            }
            
            response = requests.get(url, headers=headers, params=params_dict, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('results'):
                    # Get the first suitable image
                    image_data = data['results'][0]
                    image_url = image_data['urls']['regular']
                    
                    # Download and save the image
                    img_response = requests.get(image_url, timeout=10)
                    if img_response.status_code == 200:
                        image_path = f"temp/unsplash_image_{int(time.time())}.jpg"
                        with open(image_path, 'wb') as f:
                            f.write(img_response.content)
                        
                        return f"Downloaded Unsplash image: {image_path}"
            
            return None
            
        except Exception as e:
            print(f"Unsplash API error: {e}")
            return None

    def _try_pixabay_api(self, params: dict) -> str:
        """Try Pixabay API for free images"""
        try:
            pixabay_key = os.getenv('PIXABAY_API_KEY')
            if not pixabay_key:
                return None
            
            # Search for relevant images
            search_terms = f"{params['subject_matter']} {params['mood']} meditation"
            url = "https://pixabay.com/api/"
            params_dict = {
                "key": pixabay_key,
                "q": search_terms,
                "image_type": "photo",
                "orientation": "vertical",
                "category": "nature",
                "per_page": 5
            }
            
            response = requests.get(url, params=params_dict, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('hits'):
                    # Get the first suitable image
                    image_data = data['hits'][0]
                    image_url = image_data['largeImageURL']
                    
                    # Download and save the image
                    img_response = requests.get(image_url, timeout=10)
                    if img_response.status_code == 200:
                        image_path = f"temp/pixabay_image_{int(time.time())}.jpg"
                        with open(image_path, 'wb') as f:
                            f.write(img_response.content)
                        
                        return f"Downloaded Pixabay image: {image_path}"
            
            return None
            
        except Exception as e:
            print(f"Pixabay API error: {e}")
            return None

    def _create_fallback_image(self, params: dict, dimensions: str) -> str:
        """Create a fallback gradient image using PIL"""
        try:
            width, height = map(int, dimensions.split('x'))
            
            # Create gradient based on color scheme
            if 'golden' in params['color_scheme'].lower():
                colors = [(255, 215, 0), (255, 140, 0), (139, 69, 19)]  # Golden gradient
            elif 'blue' in params['color_scheme'].lower():
                colors = [(135, 206, 235), (70, 130, 180), (25, 25, 112)]  # Blue gradient
            elif 'green' in params['color_scheme'].lower():
                colors = [(144, 238, 144), (34, 139, 34), (0, 100, 0)]  # Green gradient
            else:
                colors = [(222, 184, 135), (160, 82, 45), (139, 69, 19)]  # Earth tones
            
            # Create gradient image
            img = Image.new('RGB', (width, height))
            pixels = img.load()
            
            for y in range(height):
                # Calculate gradient position (0 to 1)
                pos = y / height
                
                # Interpolate between colors
                if pos < 0.5:
                    # First half: interpolate between first and second color
                    t = pos * 2
                    color = tuple(int(colors[0][i] * (1 - t) + colors[1][i] * t) for i in range(3))
                else:
                    # Second half: interpolate between second and third color
                    t = (pos - 0.5) * 2
                    color = tuple(int(colors[1][i] * (1 - t) + colors[2][i] * t) for i in range(3))
                
                for x in range(width):
                    pixels[x, y] = color
            
            # Save the image
            image_path = f"temp/gradient_bg_{int(time.time())}.png"
            img.save(image_path)
            
            return f"Created gradient background image: {image_path} with {params['style']} style and {params['color_scheme']} colors"
            
        except Exception as e:
            return f"Error creating fallback image: {str(e)}"
