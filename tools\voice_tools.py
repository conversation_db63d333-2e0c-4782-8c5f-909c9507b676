from crewai.tools import BaseTool
from typing import Type
from pydantic import BaseModel, Field
import requests
import os
import time
import json
import re
from config.settings import Config

class VoiceSynthesisInput(BaseModel):
    quote: str = Field(description="Quote text to synthesize into speech")
    speech_pace: str = Field(default="moderate", description="Speech pace (slow, moderate, fast)")
    emotional_tone: str = Field(default="wise and warm", description="Emotional tone of the voice")
    emphasis_points: list = Field(default=[], description="List of words or phrases to emphasize")
    pause_locations: list = Field(default=[], description="List of locations for pauses")

class VoiceSynthesisTool(BaseTool):
    name: str = "Voice Synthesis Tool"
    description: str = "Generates high-quality voiceovers using ElevenLabs AI with <PERSON> Spuds Oxley voice"
    args_schema: Type[BaseModel] = VoiceSynthesisInput

    def __init__(self):
        super().__init__()
        # Initialize API settings
        try:
            self.api_key = Config.ELEVENLABS_API_KEY or os.getenv('ELEVENLABS_API_KEY')
        except:
            self.api_key = os.getenv('ELEVENLABS_API_KEY')

        self.voice_id = "grandpa_spuds_oxley"  # This would be the actual voice ID from ElevenLabs
        self.base_url = "https://api.elevenlabs.io/v1"

    def _run(self, quote: str, speech_pace: str = "moderate", emotional_tone: str = "wise and warm",
             emphasis_points: list = None, pause_locations: list = None) -> str:
        try:
            # Ensure temp directory exists
            os.makedirs("temp", exist_ok=True)

            if emphasis_points is None:
                emphasis_points = []
            if pause_locations is None:
                pause_locations = []

            # Extract speech parameters from quote content
            extracted_params = self._extract_speech_parameters(quote, speech_pace, emotional_tone, emphasis_points, pause_locations)

            # Try ElevenLabs API
            elevenlabs_result = self._try_elevenlabs_api(quote, extracted_params)
            if elevenlabs_result:
                return elevenlabs_result

            # Try alternative TTS APIs
            alternative_result = self._try_alternative_tts(quote, extracted_params)
            if alternative_result:
                return alternative_result

            # Fallback: Create text file with speech instructions
            fallback_result = self._create_speech_instructions(quote, extracted_params)
            return fallback_result

        except Exception as e:
            return f"Error in voice synthesis: {str(e)}"

    def _extract_speech_parameters(self, quote: str, speech_pace: str, emotional_tone: str,
                                 emphasis_points: list, pause_locations: list) -> dict:
        """Extract and enhance speech parameters based on quote content"""

        quote_lower = quote.lower()
        words = quote.split()

        # Analyze quote for optimal speech pace
        if len(words) > 20 or any(word in quote_lower for word in ['contemplate', 'reflect', 'meditate']):
            enhanced_pace = "slow and contemplative"
        elif any(word in quote_lower for word in ['action', 'now', 'today', 'moment']):
            enhanced_pace = "moderate with energy"
        else:
            enhanced_pace = speech_pace

        # Analyze for emotional tone
        if any(word in quote_lower for word in ['peace', 'calm', 'tranquil', 'serene']):
            enhanced_tone = "peaceful and calming"
        elif any(word in quote_lower for word in ['wisdom', 'truth', 'understand']):
            enhanced_tone = "wise and knowing"
        elif any(word in quote_lower for word in ['love', 'compassion', 'kindness']):
            enhanced_tone = "warm and loving"
        elif any(word in quote_lower for word in ['strength', 'courage', 'power']):
            enhanced_tone = "inspiring and strong"
        else:
            enhanced_tone = emotional_tone

        # Auto-detect emphasis points if not provided
        if not emphasis_points:
            # Look for important spiritual/philosophical words
            important_words = ['peace', 'wisdom', 'love', 'truth', 'mind', 'heart', 'soul',
                             'enlightenment', 'compassion', 'mindfulness', 'present', 'moment']
            emphasis_points = [word for word in words if word.lower() in important_words]

        # Auto-detect pause locations if not provided
        if not pause_locations:
            # Add pauses after commas, periods, and before important phrases
            pause_locations = []
            for i, word in enumerate(words):
                if word.endswith(',') or word.endswith('.') or word.endswith(';'):
                    pause_locations.append(i)
                elif word.lower() in ['but', 'however', 'therefore', 'thus', 'remember']:
                    pause_locations.append(i)

        return {
            'pace': enhanced_pace,
            'tone': enhanced_tone,
            'emphasis': emphasis_points,
            'pauses': pause_locations,
            'word_count': len(words),
            'estimated_duration': len(words) * 0.5  # Rough estimate: 0.5 seconds per word
        }

    def _try_elevenlabs_api(self, quote: str, params: dict) -> str:
        """Try ElevenLabs API for voice synthesis"""
        try:
            if not self.api_key:
                return None

            # Prepare the text with SSML-like markup for better control
            enhanced_text = self._prepare_text_with_markup(quote, params)

            # ElevenLabs API endpoint for text-to-speech
            url = f"{self.base_url}/text-to-speech/{self.voice_id}"

            headers = {
                "Accept": "audio/mpeg",
                "Content-Type": "application/json",
                "xi-api-key": self.api_key
            }

            # Voice settings based on extracted parameters
            voice_settings = self._get_voice_settings(params)

            payload = {
                "text": enhanced_text,
                "model_id": "eleven_monolingual_v1",
                "voice_settings": voice_settings
            }

            response = requests.post(url, json=payload, headers=headers, timeout=30)

            if response.status_code == 200:
                # Save the audio file
                audio_path = f"temp/voiceover_{int(time.time())}.mp3"
                with open(audio_path, 'wb') as f:
                    f.write(response.content)

                return f"Generated ElevenLabs voiceover: {audio_path} with {params['tone']} tone and {params['pace']} pace"
            else:
                print(f"ElevenLabs API error: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            print(f"ElevenLabs API error: {e}")
            return None

    def _prepare_text_with_markup(self, quote: str, params: dict) -> str:
        """Prepare text with markup for better speech synthesis"""

        enhanced_text = quote

        # Add pauses at specified locations
        words = enhanced_text.split()
        for pause_index in sorted(params['pauses'], reverse=True):
            if pause_index < len(words):
                words[pause_index] += "... "  # Add pause marker

        # Add emphasis to important words
        for emphasis_word in params['emphasis']:
            enhanced_text = enhanced_text.replace(emphasis_word, f"*{emphasis_word}*")

        # Add pace instructions
        if 'slow' in params['pace'].lower():
            enhanced_text = f"<speak rate='slow'>{enhanced_text}</speak>"
        elif 'fast' in params['pace'].lower():
            enhanced_text = f"<speak rate='fast'>{enhanced_text}</speak>"

        return enhanced_text

    def _get_voice_settings(self, params: dict) -> dict:
        """Get voice settings based on parameters"""

        # Base settings for Grandpa Spuds Oxley voice
        settings = {
            "stability": 0.75,
            "similarity_boost": 0.75,
            "style": 0.5,
            "use_speaker_boost": True
        }

        # Adjust based on tone
        if 'peaceful' in params['tone'].lower():
            settings["stability"] = 0.85
            settings["style"] = 0.3
        elif 'inspiring' in params['tone'].lower():
            settings["stability"] = 0.65
            settings["style"] = 0.7
        elif 'wise' in params['tone'].lower():
            settings["stability"] = 0.8
            settings["style"] = 0.4

        return settings

    def _try_alternative_tts(self, quote: str, params: dict) -> str:
        """Try alternative TTS APIs as fallback"""
        try:
            # Try Google Cloud Text-to-Speech (if available)
            google_result = self._try_google_tts(quote, params)
            if google_result:
                return google_result

            # Try Azure Cognitive Services (if available)
            azure_result = self._try_azure_tts(quote, params)
            if azure_result:
                return azure_result

            return None

        except Exception as e:
            print(f"Alternative TTS error: {e}")
            return None

    def _try_google_tts(self, quote: str, params: dict) -> str:
        """Try Google Cloud Text-to-Speech API"""
        try:
            google_key = os.getenv('GOOGLE_CLOUD_TTS_KEY')
            if not google_key:
                return None

            url = f"https://texttospeech.googleapis.com/v1/text:synthesize?key={google_key}"

            # Configure voice for wise, elderly male voice
            payload = {
                "input": {"text": quote},
                "voice": {
                    "languageCode": "en-US",
                    "name": "en-US-Standard-B",  # Male voice
                    "ssmlGender": "MALE"
                },
                "audioConfig": {
                    "audioEncoding": "MP3",
                    "speakingRate": 0.8 if 'slow' in params['pace'] else 1.0,
                    "pitch": -2.0  # Slightly lower pitch for elderly voice
                }
            }

            response = requests.post(url, json=payload, timeout=30)

            if response.status_code == 200:
                data = response.json()
                if 'audioContent' in data:
                    # Decode base64 audio
                    import base64
                    audio_data = base64.b64decode(data['audioContent'])

                    # Save audio file
                    audio_path = f"temp/google_tts_{int(time.time())}.mp3"
                    with open(audio_path, 'wb') as f:
                        f.write(audio_data)

                    return f"Generated Google TTS voiceover: {audio_path}"

            return None

        except Exception as e:
            print(f"Google TTS error: {e}")
            return None

    def _try_azure_tts(self, quote: str, params: dict) -> str:
        """Try Azure Cognitive Services Text-to-Speech"""
        try:
            azure_key = os.getenv('AZURE_SPEECH_KEY')
            azure_region = os.getenv('AZURE_SPEECH_REGION', 'eastus')

            if not azure_key:
                return None

            url = f"https://{azure_region}.tts.speech.microsoft.com/cognitiveservices/v1"

            headers = {
                'Ocp-Apim-Subscription-Key': azure_key,
                'Content-Type': 'application/ssml+xml',
                'X-Microsoft-OutputFormat': 'audio-16khz-128kbitrate-mono-mp3'
            }

            # Create SSML with elderly male voice
            ssml = f"""
            <speak version='1.0' xml:lang='en-US'>
                <voice xml:lang='en-US' xml:gender='Male' name='en-US-AriaNeural'>
                    <prosody rate='{self._get_azure_rate(params["pace"])}' pitch='-10%'>
                        {quote}
                    </prosody>
                </voice>
            </speak>
            """

            response = requests.post(url, headers=headers, data=ssml, timeout=30)

            if response.status_code == 200:
                audio_path = f"temp/azure_tts_{int(time.time())}.mp3"
                with open(audio_path, 'wb') as f:
                    f.write(response.content)

                return f"Generated Azure TTS voiceover: {audio_path}"

            return None

        except Exception as e:
            print(f"Azure TTS error: {e}")
            return None

    def _get_azure_rate(self, pace: str) -> str:
        """Convert pace to Azure TTS rate"""
        if 'slow' in pace.lower():
            return 'slow'
        elif 'fast' in pace.lower():
            return 'fast'
        else:
            return 'medium'

    def _create_speech_instructions(self, quote: str, params: dict) -> str:
        """Create detailed speech instructions as fallback"""
        try:
            instructions_path = f"temp/speech_instructions_{int(time.time())}.txt"

            instructions = f"""
VOICE SYNTHESIS INSTRUCTIONS
============================

Quote: "{quote}"

Speech Parameters:
- Pace: {params['pace']}
- Tone: {params['tone']}
- Estimated Duration: {params['estimated_duration']:.1f} seconds
- Word Count: {params['word_count']}

Emphasis Points: {', '.join(params['emphasis']) if params['emphasis'] else 'None specified'}
Pause Locations: {params['pauses'] if params['pauses'] else 'Natural speech pauses'}

Voice Characteristics:
- Use warm, wise, elderly male voice (Grandpa Spuds Oxley style)
- Convey {params['tone']} emotion
- Speak at {params['pace']} pace
- Add natural pauses for contemplation

Technical Notes:
- Target audio format: MP3
- Recommended sample rate: 44.1kHz
- Mono channel sufficient for voiceover
- Normalize audio levels for consistent playback
"""

            with open(instructions_path, 'w', encoding='utf-8') as f:
                f.write(instructions)

            return f"Created speech instructions file: {instructions_path} with detailed parameters for {params['tone']} delivery"

        except Exception as e:
            return f"Error creating speech instructions: {str(e)}"
