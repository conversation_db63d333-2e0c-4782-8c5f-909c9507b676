from crewai.tools import BaseTool
from typing import Type
from pydantic import BaseModel, Field
import requests
import os
import time
import json
import random

class MusicSelectionInput(BaseModel):
    quote: str = Field(description="Quote text to select music for")
    genre: str = Field(default="ambient", description="Music genre (ambient, cinematic, meditation)")
    tempo: str = Field(default="slow", description="Music tempo (slow, moderate, upbeat)")
    mood: str = Field(default="peaceful", description="Music mood")
    duration: int = Field(default=20, description="Required duration in seconds")
    volume_level: str = Field(default="background", description="Volume level relative to voice")

class MusicSelectionTool(BaseTool):
    name: str = "Music Selection Tool"
    description: str = "Selects perfect royalty-free background music from Pixabay for inspirational videos"
    args_schema: Type[BaseModel] = MusicSelectionInput

    def __init__(self):
        super().__init__()
        self.pixabay_key = os.getenv('PIXABAY_API_KEY')
        self.base_url = "https://pixabay.com/api/"

    def _run(self, quote: str, genre: str = "ambient", tempo: str = "slow", 
             mood: str = "peaceful", duration: int = 20, volume_level: str = "background") -> str:
        try:
            # Ensure temp directory exists
            os.makedirs("temp", exist_ok=True)
            
            # Extract music parameters from quote content
            extracted_params = self._extract_music_parameters(quote, genre, tempo, mood, duration, volume_level)
            
            # Try Pixabay API for music
            pixabay_result = self._try_pixabay_music_api(extracted_params)
            if pixabay_result:
                return pixabay_result
            
            # Try alternative free music APIs
            alternative_result = self._try_alternative_music_apis(extracted_params)
            if alternative_result:
                return alternative_result
            
            # Fallback: Use curated local music or create music specifications
            fallback_result = self._create_music_specifications(extracted_params)
            return fallback_result
            
        except Exception as e:
            return f"Error in music selection: {str(e)}"

    def _extract_music_parameters(self, quote: str, genre: str, tempo: str, 
                                mood: str, duration: int, volume_level: str) -> dict:
        """Extract and enhance music parameters based on quote content"""
        
        quote_lower = quote.lower()
        
        # Analyze quote for optimal music genre
        if any(word in quote_lower for word in ['peace', 'calm', 'tranquil', 'serene']):
            enhanced_genre = "ambient meditation"
        elif any(word in quote_lower for word in ['wisdom', 'ancient', 'temple', 'spiritual']):
            enhanced_genre = "mystical ambient"
        elif any(word in quote_lower for word in ['nature', 'mountain', 'water', 'forest']):
            enhanced_genre = "nature sounds ambient"
        elif any(word in quote_lower for word in ['light', 'bright', 'dawn', 'hope']):
            enhanced_genre = "uplifting cinematic"
        elif any(word in quote_lower for word in ['love', 'heart', 'compassion']):
            enhanced_genre = "warm emotional"
        else:
            enhanced_genre = genre
        
        # Analyze for optimal tempo
        word_count = len(quote.split())
        if word_count > 20 or any(word in quote_lower for word in ['contemplate', 'reflect', 'meditate']):
            enhanced_tempo = "very slow 60-70 BPM"
        elif any(word in quote_lower for word in ['action', 'now', 'moment', 'today']):
            enhanced_tempo = "moderate 80-90 BPM"
        else:
            enhanced_tempo = f"{tempo} contemplative"
        
        # Analyze for mood
        if any(word in quote_lower for word in ['joy', 'happiness', 'celebration']):
            enhanced_mood = "uplifting and joyful"
        elif any(word in quote_lower for word in ['strength', 'courage', 'power']):
            enhanced_mood = "inspiring and empowering"
        elif any(word in quote_lower for word in ['mystery', 'unknown', 'deep']):
            enhanced_mood = "mystical and contemplative"
        else:
            enhanced_mood = mood
        
        # Calculate optimal duration (add buffer for fade in/out)
        estimated_speech_duration = len(quote.split()) * 0.5  # 0.5 seconds per word
        optimal_duration = max(duration, int(estimated_speech_duration + 4))  # +4 for fades
        
        return {
            'genre': enhanced_genre,
            'tempo': enhanced_tempo,
            'mood': enhanced_mood,
            'duration': optimal_duration,
            'volume_level': volume_level,
            'search_terms': self._generate_search_terms(enhanced_genre, enhanced_mood),
            'quote_essence': quote_lower
        }

    def _generate_search_terms(self, genre: str, mood: str) -> list:
        """Generate search terms for music APIs"""
        
        base_terms = ['meditation', 'ambient', 'peaceful', 'instrumental']
        
        if 'mystical' in genre.lower():
            base_terms.extend(['mystical', 'spiritual', 'ethereal'])
        elif 'nature' in genre.lower():
            base_terms.extend(['nature', 'forest', 'water', 'birds'])
        elif 'cinematic' in genre.lower():
            base_terms.extend(['cinematic', 'orchestral', 'epic'])
        
        if 'uplifting' in mood.lower():
            base_terms.extend(['uplifting', 'inspiring', 'hopeful'])
        elif 'contemplative' in mood.lower():
            base_terms.extend(['contemplative', 'reflective', 'zen'])
        
        return base_terms

    def _try_pixabay_music_api(self, params: dict) -> str:
        """Try Pixabay API for royalty-free music"""
        try:
            if not self.pixabay_key:
                return None
            
            # Search for music based on parameters
            for search_term in params['search_terms'][:3]:  # Try top 3 search terms
                url = self.base_url
                params_dict = {
                    "key": self.pixabay_key,
                    "q": search_term,
                    "media_type": "music",
                    "category": "music",
                    "min_duration": max(15, params['duration'] - 10),
                    "per_page": 10
                }
                
                response = requests.get(url, params=params_dict, timeout=15)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('hits'):
                        # Find the best matching track
                        best_track = self._select_best_track(data['hits'], params)
                        if best_track:
                            # Download the music file
                            download_result = self._download_music_file(best_track, params)
                            if download_result:
                                return download_result
            
            return None
            
        except Exception as e:
            print(f"Pixabay music API error: {e}")
            return None

    def _select_best_track(self, tracks: list, params: dict) -> dict:
        """Select the best matching track from search results"""
        
        scored_tracks = []
        
        for track in tracks:
            score = 0
            tags = track.get('tags', '').lower()
            
            # Score based on genre match
            if any(term in tags for term in params['genre'].lower().split()):
                score += 3
            
            # Score based on mood match
            if any(term in tags for term in params['mood'].lower().split()):
                score += 2
            
            # Score based on duration (prefer tracks close to required duration)
            track_duration = track.get('duration', 0)
            if abs(track_duration - params['duration']) <= 10:
                score += 2
            elif abs(track_duration - params['duration']) <= 30:
                score += 1
            
            # Prefer higher quality tracks
            if track.get('downloads', 0) > 1000:
                score += 1
            
            scored_tracks.append((score, track))
        
        # Sort by score and return the best track
        if scored_tracks:
            scored_tracks.sort(key=lambda x: x[0], reverse=True)
            return scored_tracks[0][1]
        
        return None

    def _download_music_file(self, track: dict, params: dict) -> str:
        """Download the selected music file"""
        try:
            # Get the download URL (usually the preview URL for free tracks)
            download_url = track.get('previewURL') or track.get('webformatURL')
            
            if not download_url:
                return None
            
            # Download the file
            response = requests.get(download_url, timeout=30)
            
            if response.status_code == 200:
                # Determine file extension
                file_ext = 'mp3'  # Default to mp3
                if 'ogg' in download_url.lower():
                    file_ext = 'ogg'
                elif 'wav' in download_url.lower():
                    file_ext = 'wav'
                
                # Save the file
                music_path = f"temp/background_music_{int(time.time())}.{file_ext}"
                with open(music_path, 'wb') as f:
                    f.write(response.content)
                
                # Create metadata file
                metadata = {
                    'file_path': music_path,
                    'title': track.get('tags', 'Background Music'),
                    'duration': track.get('duration', params['duration']),
                    'genre': params['genre'],
                    'mood': params['mood'],
                    'tempo': params['tempo'],
                    'volume_level': params['volume_level'],
                    'source': 'Pixabay',
                    'license': 'Royalty-free',
                    'recommended_volume': self._get_recommended_volume(params['volume_level'])
                }
                
                metadata_path = f"temp/music_metadata_{int(time.time())}.json"
                with open(metadata_path, 'w') as f:
                    json.dump(metadata, f, indent=2)
                
                return f"Downloaded Pixabay music: {music_path} ({params['genre']} style, {params['mood']} mood, {params['tempo']} tempo) with metadata: {metadata_path}"
            
            return None
            
        except Exception as e:
            print(f"Music download error: {e}")
            return None

    def _get_recommended_volume(self, volume_level: str) -> float:
        """Get recommended volume level as a percentage"""
        
        volume_map = {
            'background': 0.25,  # 25% for background music
            'subtle': 0.15,      # 15% for very subtle background
            'prominent': 0.4,    # 40% for more prominent music
            'balanced': 0.3      # 30% for balanced mix
        }
        
        return volume_map.get(volume_level.lower(), 0.25)

    def _try_alternative_music_apis(self, params: dict) -> str:
        """Try alternative free music APIs"""
        try:
            # Try Freesound.org API
            freesound_result = self._try_freesound_api(params)
            if freesound_result:
                return freesound_result
            
            # Try YouTube Audio Library (if available)
            youtube_result = self._try_youtube_audio_library(params)
            if youtube_result:
                return youtube_result
            
            return None
            
        except Exception as e:
            print(f"Alternative music API error: {e}")
            return None

    def _try_freesound_api(self, params: dict) -> str:
        """Try Freesound.org API for ambient sounds"""
        try:
            freesound_key = os.getenv('FREESOUND_API_KEY')
            if not freesound_key:
                return None
            
            # Search for ambient sounds
            search_query = f"{params['mood']} ambient meditation"
            url = "https://freesound.org/apiv2/search/text/"
            
            headers = {"Authorization": f"Token {freesound_key}"}
            params_dict = {
                "query": search_query,
                "filter": f"duration:[{params['duration']-10} TO {params['duration']+30}]",
                "fields": "id,name,previews,duration,tags,download",
                "page_size": 5
            }
            
            response = requests.get(url, headers=headers, params=params_dict, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('results'):
                    # Get the first suitable sound
                    sound = data['results'][0]
                    preview_url = sound['previews']['preview-hq-mp3']
                    
                    # Download the preview
                    audio_response = requests.get(preview_url, timeout=15)
                    if audio_response.status_code == 200:
                        music_path = f"temp/freesound_music_{int(time.time())}.mp3"
                        with open(music_path, 'wb') as f:
                            f.write(audio_response.content)
                        
                        return f"Downloaded Freesound music: {music_path} ({sound['name']})"
            
            return None
            
        except Exception as e:
            print(f"Freesound API error: {e}")
            return None

    def _try_youtube_audio_library(self, params: dict) -> str:
        """Try YouTube Audio Library (conceptual - would need actual implementation)"""
        # This would require YouTube API integration and proper licensing
        # For now, return None to trigger fallback
        return None

    def _create_music_specifications(self, params: dict) -> str:
        """Create detailed music specifications as fallback"""
        try:
            # Check if we have any local music files
            local_music = self._check_local_music_library(params)
            if local_music:
                return local_music
            
            # Create detailed specifications file
            specs_path = f"temp/music_specifications_{int(time.time())}.json"
            
            specifications = {
                "music_requirements": {
                    "genre": params['genre'],
                    "tempo": params['tempo'],
                    "mood": params['mood'],
                    "duration": params['duration'],
                    "volume_level": params['volume_level'],
                    "recommended_volume_percentage": self._get_recommended_volume(params['volume_level']) * 100
                },
                "search_suggestions": params['search_terms'],
                "alternative_sources": [
                    "YouTube Audio Library",
                    "Freesound.org",
                    "Zapsplat.com",
                    "Adobe Stock Audio",
                    "Pond5.com"
                ],
                "local_alternatives": [
                    "assets/chinese_flute_sounds/",
                    "assets/backgrounds/audio/"
                ],
                "technical_specs": {
                    "format": "MP3 or WAV",
                    "sample_rate": "44.1kHz",
                    "bit_rate": "128kbps minimum",
                    "channels": "Stereo preferred, mono acceptable",
                    "fade_in": "1 second",
                    "fade_out": "2 seconds"
                }
            }
            
            with open(specs_path, 'w') as f:
                json.dump(specifications, f, indent=2)
            
            return f"Created music specifications: {specs_path} for {params['genre']} style {params['mood']} music at {params['volume_level']} volume level"
            
        except Exception as e:
            return f"Error creating music specifications: {str(e)}"

    def _check_local_music_library(self, params: dict) -> str:
        """Check for suitable local music files"""
        try:
            # Check common music directories
            music_dirs = [
                "assets/chinese_flute_sounds/",
                "assets/backgrounds/audio/",
                "assets/music/",
                "temp/music/"
            ]
            
            for music_dir in music_dirs:
                if os.path.exists(music_dir):
                    music_files = [f for f in os.listdir(music_dir) 
                                 if f.lower().endswith(('.mp3', '.wav', '.ogg'))]
                    
                    if music_files:
                        # Select a random suitable file
                        selected_file = random.choice(music_files)
                        full_path = os.path.join(music_dir, selected_file)
                        
                        return f"Selected local music file: {full_path} (genre: {params['genre']}, mood: {params['mood']}, volume: {params['volume_level']})"
            
            return None
            
        except Exception as e:
            print(f"Local music check error: {e}")
            return None
